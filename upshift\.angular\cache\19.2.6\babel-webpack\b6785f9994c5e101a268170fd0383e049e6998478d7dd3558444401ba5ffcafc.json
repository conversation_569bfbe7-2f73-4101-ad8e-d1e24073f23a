{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/work-things/vlastne/upshift_project/upshift/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar _TodayPage;\nimport { inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { QuestService } from '../../services/quest.service';\nimport { SideQuestService } from '../../services/sidequest.service';\nimport { UserService } from '../../services/user.service';\nimport { SupabaseService } from '../../services/supabase.service';\nimport { Quest } from '../../models/quest.model';\nimport { Subscription, forkJoin, map, of, switchMap, take, firstValueFrom } from 'rxjs';\nimport { NavigationComponent } from '../../components/navigation/navigation.component';\nimport { CelebrationComponent } from '../../components/celebration/celebration.component';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { PreferencesService } from '../../services/preferences.service';\nimport { EmojiInputDirective } from '../../directives/emoji-input.directive';\nimport { StreakCalculatorService } from '../../services/streak-calculator';\nimport { HeaderComponent } from 'src/app/components/header/header.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/angular\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nconst _c0 = () => [0, 0.25, 0.5, 0.75, 1];\nconst _c1 = () => [\"M\", \"T\", \"W\", \"T\", \"F\", \"S\", \"S\"];\nfunction TodayPage_div_8__svg_circle_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelement(0, \"circle\", 27);\n  }\n  if (rf & 2) {\n    const date_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassProp(\"low\", date_r2.completion_percentage < 50);\n    i0.ɵɵattribute(\"stroke-dasharray\", date_r2.completion_percentage * 81.68 / 100 + \", 81.68\")(\"data-date\", date_r2.date);\n  }\n}\nfunction TodayPage_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"ion-text\", 22);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 23);\n    i0.ɵɵlistener(\"click\", function TodayPage_div_8_Template_div_click_3_listener() {\n      const date_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(!date_r2.is_future && ctx_r2.selectDate(date_r2));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(4, \"svg\", 24);\n    i0.ɵɵelement(5, \"circle\", 25);\n    i0.ɵɵtemplate(6, TodayPage_div_8__svg_circle_6_Template, 1, 4, \"circle\", 26);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const date_r2 = ctx.$implicit;\n    const i_r4 = ctx.index;\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", date_r2.is_today)(\"selected\", date_r2.is_selected && !date_r2.is_today)(\"unselected\", !date_r2.is_selected);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpureFunction0(12, _c1)[i_r4], \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"selected\", date_r2.is_selected)(\"disabled\", date_r2.is_future);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !date_r2.is_future);\n  }\n}\nfunction TodayPage_ion_card_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-card\", 28)(1, \"ion-card-header\")(2, \"h2\");\n    i0.ɵɵtext(3, \"No quests found..\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"ion-card-content\")(5, \"ion-row\")(6, \"ion-col\", 29)(7, \"ion-text\");\n    i0.ɵɵtext(8, \"No quests found. Try adding a quest ;)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"ion-col\", 30);\n    i0.ɵɵelement(10, \"ion-icon\", 31);\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction TodayPage_ion_card_22_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"ion-range\", 41);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TodayPage_ion_card_22_div_11_Template_ion_range_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const quest_r6 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(quest_r6.value_achieved, $event) || (quest_r6.value_achieved = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ionChange\", function TodayPage_ion_card_22_div_11_Template_ion_range_ionChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const quest_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.updateQuestProgress(quest_r6, $event));\n    })(\"ionInput\", function TodayPage_ion_card_22_div_11_Template_ion_range_ionInput_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView($event.target && ctx_r2.updateSliderBackground($event.target));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"ion-text\", 42);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const quest_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMapInterpolate1(\"--progress-value: \", quest_r6.value_achieved / quest_r6.goal_value * 100, \"%\");\n    i0.ɵɵproperty(\"max\", quest_r6.goal_value);\n    i0.ɵɵtwoWayProperty(\"ngModel\", quest_r6.value_achieved);\n    i0.ɵɵproperty(\"step\", 1);\n    i0.ɵɵattribute(\"data-quest-id\", quest_r6.id)(\"data-quest-type\", quest_r6.quest_type);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate4(\" \", quest_r6.value_achieved, \"\", quest_r6.goal_unit === \"min\" ? \"m\" : quest_r6.goal_unit === \"hr\" ? \"h\" : \"s\", \"/\", quest_r6.goal_value, \"\", quest_r6.goal_unit === \"min\" ? \"m\" : quest_r6.goal_unit === \"hr\" ? \"h\" : \"s\", \" \");\n  }\n}\nfunction TodayPage_ion_card_22_div_12_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const quest_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", quest_r6.goal_unit, \" \");\n  }\n}\nfunction TodayPage_ion_card_22_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"ion-range\", 41);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TodayPage_ion_card_22_div_12_Template_ion_range_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const quest_r6 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(quest_r6.value_achieved, $event) || (quest_r6.value_achieved = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ionChange\", function TodayPage_ion_card_22_div_12_Template_ion_range_ionChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const quest_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.updateQuestProgress(quest_r6, $event));\n    })(\"ionInput\", function TodayPage_ion_card_22_div_12_Template_ion_range_ionInput_1_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView($event.target && ctx_r2.updateSliderBackground($event.target));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"ion-text\", 42);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, TodayPage_ion_card_22_div_12_ng_container_4_Template, 2, 1, \"ng-container\", 44);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const quest_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMapInterpolate1(\"--progress-value: \", quest_r6.value_achieved / quest_r6.goal_value * 100, \"%\");\n    i0.ɵɵproperty(\"max\", quest_r6.goal_value);\n    i0.ɵɵtwoWayProperty(\"ngModel\", quest_r6.value_achieved);\n    i0.ɵɵproperty(\"step\", 1);\n    i0.ɵɵattribute(\"data-quest-id\", quest_r6.id)(\"data-quest-type\", quest_r6.quest_type);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", quest_r6.value_achieved, \"/\", quest_r6.goal_value, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", quest_r6.goal_unit !== \"count\");\n  }\n}\nfunction TodayPage_ion_card_22_ion_text_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-text\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const quest_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\uD83D\\uDD25\", quest_r6.streak, \"d \");\n  }\n}\nfunction TodayPage_ion_card_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-card\", 32);\n    i0.ɵɵlistener(\"click\", function TodayPage_ion_card_22_Template_ion_card_click_0_listener() {\n      const quest_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleQuest(quest_r6));\n    });\n    i0.ɵɵelementStart(1, \"ion-row\")(2, \"ion-col\", 33)(3, \"div\", 34);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"ion-col\", 35)(6, \"h3\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"ion-text\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 36);\n    i0.ɵɵtemplate(11, TodayPage_ion_card_22_div_11_Template, 4, 12, \"div\", 37)(12, TodayPage_ion_card_22_div_12_Template, 5, 11, \"div\", 38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"ion-col\", 33);\n    i0.ɵɵtemplate(14, TodayPage_ion_card_22_ion_text_14_Template, 2, 1, \"ion-text\", 39);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const quest_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"completed\", quest_r6.completed);\n    i0.ɵɵattribute(\"data-quest-id\", quest_r6.id)(\"data-regular-quest\", true);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", quest_r6.emoji, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(quest_r6.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(quest_r6.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", quest_r6.goal_unit === \"time\" || quest_r6.goal_unit === \"min\" || quest_r6.goal_unit === \"hr\" || quest_r6.goal_unit === \"sec\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", quest_r6.goal_unit !== \"time\" && quest_r6.goal_unit !== \"min\" && quest_r6.goal_unit !== \"hr\" && quest_r6.goal_unit !== \"sec\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isSameDay(ctx_r2.selectedDate, ctx_r2.getToday()));\n  }\n}\nfunction TodayPage_ion_card_29_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\uD83D\\uDD25\", ctx_r2.dailyQuest.streak, \"d \");\n  }\n}\nfunction TodayPage_ion_card_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-card\", 46);\n    i0.ɵɵlistener(\"click\", function TodayPage_ion_card_29_Template_ion_card_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleSideQuest(ctx_r2.dailyQuest));\n    });\n    i0.ɵɵelementStart(1, \"ion-row\")(2, \"ion-col\", 33)(3, \"div\", 34);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"ion-col\", 35)(6, \"h3\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"ion-text\", 47);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"ion-col\", 33);\n    i0.ɵɵtemplate(11, TodayPage_ion_card_29_div_11_Template, 2, 1, \"div\", 39);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"completed\", ctx_r2.dailyQuest.completed);\n    i0.ɵɵattribute(\"data-quest-id\", ctx_r2.dailyQuest.id)(\"data-side-quest\", true);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.dailyQuest.emoji, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.dailyQuest.current_quest.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.dailyQuest.current_quest.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isSameDay(ctx_r2.selectedDate, ctx_r2.getToday()));\n  }\n}\nfunction TodayPage_ion_card_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-card\", 48)(1, \"ion-card-header\")(2, \"ion-card-title\");\n    i0.ɵɵtext(3, \" Daily Side Quest \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"ion-card-content\")(5, \"ion-text\");\n    i0.ɵɵtext(6, \" No daily side quests are currently available. Please check back later or contact an administrator. \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction TodayPage_ng_template_32_div_60_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 118);\n    i0.ɵɵelement(1, \"ion-icon\", 119);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"You already have a high priority quest in this category\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TodayPage_ng_template_32_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 69)(1, \"ion-select\", 114);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TodayPage_ng_template_32_div_60_Template_ion_select_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.newQuest.priority, $event) || (ctx_r2.newQuest.priority = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(2, \"ion-select-option\", 115);\n    i0.ɵɵtext(3, \"\\u2B50 Basic\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"ion-select-option\", 116);\n    i0.ɵɵtext(5, \"\\uD83D\\uDD25 High Priority\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"label\", 68);\n    i0.ɵɵtext(7, \"Priority Level\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, TodayPage_ng_template_32_div_60_div_8_Template, 4, 0, \"div\", 117);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.newQuest.priority);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.hasHighPriorityQuest);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.hasHighPriorityQuest);\n  }\n}\nfunction TodayPage_ng_template_32_div_121_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 123)(1, \"ion-checkbox\", 124);\n    i0.ɵɵlistener(\"ionChange\", function TodayPage_ng_template_32_div_121_div_4_Template_ion_checkbox_ionChange_1_listener($event) {\n      const day_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.updateDaysOfWeek($event, day_r13.value));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 125);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const day_r13 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"day-\" + day_r13.value.toLowerCase())(\"value\", day_r13.value);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"for\", \"day-\" + day_r13.value.toLowerCase());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", day_r13.label, \" \");\n  }\n}\nfunction TodayPage_ng_template_32_div_121_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 120)(1, \"h4\");\n    i0.ɵɵtext(2, \"Select Days of Week\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 121);\n    i0.ɵɵtemplate(4, TodayPage_ng_template_32_div_121_div_4_Template, 4, 4, \"div\", 122);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.weekDays);\n  }\n}\nfunction TodayPage_ng_template_32_div_122_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 128)(1, \"ion-checkbox\", 129);\n    i0.ɵɵlistener(\"ionChange\", function TodayPage_ng_template_32_div_122_div_4_Template_ion_checkbox_ionChange_1_listener($event) {\n      const day_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.updateDaysOfMonth($event, day_r15));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 130);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const day_r15 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"month-day-\" + day_r15)(\"value\", day_r15);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"for\", \"month-day-\" + day_r15);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", day_r15, \" \");\n  }\n}\nfunction TodayPage_ng_template_32_div_122_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 120)(1, \"h4\");\n    i0.ɵɵtext(2, \"Select Days of Month\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 126);\n    i0.ɵɵtemplate(4, TodayPage_ng_template_32_div_122_div_4_Template, 4, 4, \"div\", 127);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.monthDays);\n  }\n}\nfunction TodayPage_ng_template_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-header\", 49)(1, \"ion-toolbar\")(2, \"ion-title\", 50)(3, \"div\", 51)(4, \"div\", 52);\n    i0.ɵɵtext(5, \"\\u2728\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7, \"Create New Quest\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"ion-buttons\", 53)(9, \"ion-button\", 54);\n    i0.ɵɵlistener(\"click\", function TodayPage_ng_template_32_Template_ion_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.closeAddQuestModal());\n    });\n    i0.ɵɵelement(10, \"ion-icon\", 55);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(11, \"ion-content\", 56)(12, \"div\", 57)(13, \"form\", 58, 0);\n    i0.ɵɵlistener(\"ngSubmit\", function TodayPage_ng_template_32_Template_form_ngSubmit_13_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.createQuest());\n    });\n    i0.ɵɵelementStart(15, \"div\", 59)(16, \"div\", 60)(17, \"h3\");\n    i0.ɵɵtext(18, \"Quest Identity\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(19, \"div\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 62)(21, \"div\", 63)(22, \"ion-input\", 64);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TodayPage_ng_template_32_Template_ion_input_ngModelChange_22_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.newQuest.emoji, $event) || (ctx_r2.newQuest.emoji = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"label\", 65);\n    i0.ɵɵtext(24, \"Icon\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 66)(26, \"ion-input\", 67);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TodayPage_ng_template_32_Template_ion_input_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.newQuest.name, $event) || (ctx_r2.newQuest.name = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"label\", 68);\n    i0.ɵɵtext(28, \"Quest Name\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(29, \"div\", 69)(30, \"ion-textarea\", 70);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TodayPage_ng_template_32_Template_ion_textarea_ngModelChange_30_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.newQuest.description, $event) || (ctx_r2.newQuest.description = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"label\", 68);\n    i0.ɵɵtext(32, \"Description (Optional)\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(33, \"div\", 59)(34, \"div\", 60)(35, \"h3\");\n    i0.ɵɵtext(36, \"Configuration\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(37, \"div\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"div\", 69)(39, \"ion-select\", 71);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TodayPage_ng_template_32_Template_ion_select_ngModelChange_39_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.newQuest.quest_type, $event) || (ctx_r2.newQuest.quest_type = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(40, \"ion-select-option\", 72);\n    i0.ɵɵtext(41, \"\\uD83C\\uDFD7\\uFE0F Build Habit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"ion-select-option\", 73);\n    i0.ɵɵtext(43, \"\\uD83D\\uDEAB Quit Habit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(44, \"label\", 68);\n    i0.ɵɵtext(45, \"Quest Type\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 69)(47, \"ion-select\", 74);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TodayPage_ng_template_32_Template_ion_select_ngModelChange_47_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.newQuest.category, $event) || (ctx_r2.newQuest.category = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ionChange\", function TodayPage_ng_template_32_Template_ion_select_ionChange_47_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.checkCategoryPriority($event));\n    });\n    i0.ɵɵelementStart(48, \"ion-select-option\", 75);\n    i0.ɵɵtext(49, \"Select a category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"ion-select-option\", 76);\n    i0.ɵɵtext(51, \"\\uD83D\\uDCAA Strength\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"ion-select-option\", 77);\n    i0.ɵɵtext(53, \"\\uD83D\\uDCB0 Money\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"ion-select-option\", 78);\n    i0.ɵɵtext(55, \"\\uD83C\\uDFE5 Health\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"ion-select-option\", 79);\n    i0.ɵɵtext(57, \"\\uD83D\\uDCDA Knowledge\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(58, \"label\", 68);\n    i0.ɵɵtext(59, \"Category\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(60, TodayPage_ng_template_32_div_60_Template, 9, 3, \"div\", 80);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(61, \"div\", 59)(62, \"div\", 60)(63, \"h3\");\n    i0.ɵɵtext(64, \"Goal & Tracking\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(65, \"div\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"div\", 81)(67, \"div\", 82)(68, \"ion-input\", 83);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TodayPage_ng_template_32_Template_ion_input_ngModelChange_68_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.newQuest.goal_value, $event) || (ctx_r2.newQuest.goal_value = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(69, \"label\", 68);\n    i0.ɵɵtext(70, \"Target\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(71, \"div\", 84)(72, \"ion-select\", 85);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TodayPage_ng_template_32_Template_ion_select_ngModelChange_72_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.newQuest.goal_unit, $event) || (ctx_r2.newQuest.goal_unit = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(73, \"ion-select-option\", 86);\n    i0.ɵɵtext(74, \"times\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(75, \"ion-select-option\", 87);\n    i0.ɵɵtext(76, \"steps\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(77, \"ion-select-option\", 88);\n    i0.ɵɵtext(78, \"meters\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(79, \"ion-select-option\", 89);\n    i0.ɵɵtext(80, \"kilometers\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(81, \"ion-select-option\", 90);\n    i0.ɵɵtext(82, \"seconds\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(83, \"ion-select-option\", 91);\n    i0.ɵɵtext(84, \"minutes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(85, \"ion-select-option\", 92);\n    i0.ɵɵtext(86, \"hours\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(87, \"ion-select-option\", 93);\n    i0.ɵɵtext(88, \"calories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(89, \"ion-select-option\", 94);\n    i0.ɵɵtext(90, \"grams\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(91, \"ion-select-option\", 95);\n    i0.ɵɵtext(92, \"milligrams\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(93, \"ion-select-option\", 96);\n    i0.ɵɵtext(94, \"liters\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(95, \"ion-select-option\", 97);\n    i0.ɵɵtext(96, \"drinks\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(97, \"ion-select-option\", 98);\n    i0.ɵɵtext(98, \"pages\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(99, \"ion-select-option\", 99);\n    i0.ɵɵtext(100, \"books\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(101, \"ion-select-option\", 100);\n    i0.ɵɵtext(102, \"percent\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(103, \"ion-select-option\", 101);\n    i0.ɵɵtext(104, \"euros\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(105, \"ion-select-option\", 102);\n    i0.ɵɵtext(106, \"dollars\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(107, \"ion-select-option\", 103);\n    i0.ɵɵtext(108, \"pounds\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(109, \"label\", 68);\n    i0.ɵɵtext(110, \"Unit\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(111, \"div\", 69)(112, \"ion-select\", 104);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TodayPage_ng_template_32_Template_ion_select_ngModelChange_112_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.newQuest.goal_period, $event) || (ctx_r2.newQuest.goal_period = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ionChange\", function TodayPage_ng_template_32_Template_ion_select_ionChange_112_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.updatePeriodDisplay());\n    });\n    i0.ɵɵelementStart(113, \"ion-select-option\", 105);\n    i0.ɵɵtext(114, \"\\uD83D\\uDCC5 Every Day\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(115, \"ion-select-option\", 106);\n    i0.ɵɵtext(116, \"\\uD83D\\uDCC6 Specific Days of Week\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(117, \"ion-select-option\", 107);\n    i0.ɵɵtext(118, \"\\uD83D\\uDDD3\\uFE0F Specific Days of Month\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(119, \"label\", 68);\n    i0.ɵɵtext(120, \"Frequency\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(121, TodayPage_ng_template_32_div_121_Template, 5, 1, \"div\", 108)(122, TodayPage_ng_template_32_div_122_Template, 5, 1, \"div\", 108);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(123, \"ion-footer\", 109)(124, \"ion-toolbar\")(125, \"div\", 110)(126, \"ion-button\", 111);\n    i0.ɵɵlistener(\"click\", function TodayPage_ng_template_32_Template_ion_button_click_126_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.closeAddQuestModal());\n    });\n    i0.ɵɵtext(127, \" Cancel \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(128, \"ion-button\", 112);\n    i0.ɵɵlistener(\"click\", function TodayPage_ng_template_32_Template_ion_button_click_128_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.createQuest());\n    });\n    i0.ɵɵelement(129, \"ion-icon\", 113);\n    i0.ɵɵtext(130, \" Create Quest \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const questForm_r16 = i0.ɵɵreference(14);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(22);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.newQuest.emoji);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.newQuest.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.newQuest.description);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.newQuest.quest_type);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.newQuest.category);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.newQuest.category);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.newQuest.goal_value);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.newQuest.goal_unit);\n    i0.ɵɵadvance(40);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.newQuest.goal_period);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.newQuest.goal_period === \"week\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.newQuest.goal_period === \"month\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"disabled\", !(questForm_r16 == null ? null : questForm_r16.valid));\n  }\n}\nfunction TodayPage_app_celebration_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-celebration\", 131);\n    i0.ɵɵlistener(\"close\", function TodayPage_app_celebration_33_Template_app_celebration_close_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.closeCelebration());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"user\", ctx_r2.currentUser)(\"date\", ctx_r2.formatDate(ctx_r2.selectedDate));\n  }\n}\nexport class TodayPage {\n  // Method to load daily side quest\n  loadDailySideQuest() {\n    // Only load for today's date\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    const selectedDate = new Date(this.selectedDate);\n    selectedDate.setHours(0, 0, 0, 0);\n    const isTodaySelected = selectedDate.getTime() === today.getTime();\n    // Reset dailyQuest if not today's date\n    if (!isTodaySelected) {\n      this.dailyQuest = null;\n      return;\n    }\n    if (this.showSidequests && isTodaySelected && this.userId) {\n      // Use the ensureUserHasDailySideQuests method\n      this.sideQuestService.ensureUserHasDailySideQuests(this.userId).pipe(take(1)).subscribe({\n        next: sideQuests => {\n          if (sideQuests && sideQuests.length > 0) {\n            const sideQuest = sideQuests[0];\n            // Get the quest details from the pool\n            this.supabaseService.getClient().from('daily_sidequest_pool').select('*').eq('id', sideQuest.current_quest_id).single().then(response => {\n              if (response.error) {\n                return;\n              }\n              const questDetails = response.data;\n              // Create the daily quest object\n              this.dailyQuest = {\n                id: sideQuest.id,\n                current_quest: {\n                  id: sideQuest.current_quest_id,\n                  name: questDetails.name || 'Daily Side Quest',\n                  description: questDetails.description || 'Complete this daily side quest',\n                  goal_value: questDetails.goal_value || 1,\n                  goal_unit: questDetails.goal_unit || 'count'\n                },\n                streak: sideQuest.streak || 0,\n                completed: sideQuest.completed || false,\n                value_achieved: sideQuest.value_achieved || 0,\n                emoji: questDetails.emoji || '🎯'\n              };\n            });\n          } else {\n            this.dailyQuest = null;\n          }\n        },\n        error: () => {\n          this.dailyQuest = null;\n        }\n      });\n    }\n  }\n  constructor() {\n    // User data\n    this.user$ = of(null);\n    this.userId = null;\n    this.showSidequests = true;\n    // Date and calendar\n    this.selectedDate = new Date();\n    this.weekDates = [];\n    this.dayNames = ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'];\n    this.headerText = 'Today';\n    this.weekOffset = 0;\n    // Quests\n    this.quests = [];\n    this.dailyQuest = null;\n    // Cache for quest data to improve performance\n    this.questCache = {};\n    // Flag to track if we're currently loading data\n    this.isLoadingData = false;\n    // Add Quest Modal\n    this.showAddQuestModal = false;\n    this.newQuest = this.getEmptyQuest();\n    this.hasHighPriorityQuest = false;\n    // Celebration Modal\n    this.showCelebration = false;\n    this.currentUser = null;\n    this.celebrationShownDates = [];\n    // Days selection for new quest\n    this.weekDays = [{\n      value: 'Sun',\n      label: 'Su'\n    }, {\n      value: 'Mon',\n      label: 'Mo'\n    }, {\n      value: 'Tue',\n      label: 'Tu'\n    }, {\n      value: 'Wed',\n      label: 'We'\n    }, {\n      value: 'Thu',\n      label: 'Th'\n    }, {\n      value: 'Fri',\n      label: 'Fr'\n    }, {\n      value: 'Sat',\n      label: 'Sa'\n    }];\n    this.monthDays = Array.from({\n      length: 31\n    }, (_, i) => i + 1);\n    this.selectedDaysOfWeek = [];\n    this.selectedDaysOfMonth = [];\n    // Use inject instead of constructor injection\n    this.questService = inject(QuestService);\n    this.sideQuestService = inject(SideQuestService);\n    this.userService = inject(UserService);\n    this.supabaseService = inject(SupabaseService);\n    this.route = inject(ActivatedRoute);\n    this.router = inject(Router);\n    this.preferencesService = inject(PreferencesService);\n    this.streakCalculator = inject(StreakCalculatorService);\n    this.isRedirecting = false; // Flag to prevent multiple redirects\n    // Cache for week date progress\n    this.weekProgressCache = {};\n    // Flag to track if we're currently changing weeks\n    this.isChangingWeek = false;\n    // Map to track which quests are currently being toggled\n    this.togglingQuestIds = {};\n    // Map to track which quests are currently being updated\n    this.updatingQuestIds = {};\n    // Map to track which side quests are currently being toggled\n    this.togglingSideQuestIds = {};\n    // Subscribe to query params to get date and week_offset from URL\n    this.route.queryParams.subscribe(params => {\n      const dateParam = params['date'];\n      const weekOffsetParam = params['week_offset'];\n      console.log('TodayPage: Date param from URL query:', dateParam);\n      console.log('TodayPage: Week offset param from URL query:', weekOffsetParam);\n      // Process week offset parameter\n      if (weekOffsetParam !== undefined) {\n        try {\n          this.weekOffset = parseInt(weekOffsetParam);\n          console.log('TodayPage: Week offset set to:', this.weekOffset);\n        } catch (error) {\n          console.error('TodayPage: Error parsing week offset:', error);\n          this.weekOffset = 0;\n        }\n      } else {\n        this.weekOffset = 0;\n      }\n      // Process date parameter\n      if (dateParam) {\n        try {\n          // Validate date format (YYYY-MM-DD)\n          if (/^\\d{4}-\\d{2}-\\d{2}$/.test(dateParam)) {\n            this.selectedDate = new Date(dateParam);\n            console.log('TodayPage: Selected date from URL query:', this.selectedDate);\n          } else {\n            console.error('TodayPage: Invalid date format in URL query:', dateParam);\n            this.selectedDate = new Date(); // Default to today\n          }\n        } catch (error) {\n          console.error('TodayPage: Error parsing date from URL query:', error);\n          this.selectedDate = new Date(); // Default to today\n        }\n      } else {\n        this.selectedDate = new Date(); // Default to today\n      }\n      // Initialize week dates based on selected date and week offset\n      this.generateWeekDates();\n      // Update header text and load data\n      this.updateHeaderText();\n      // Only load data if we have a userId\n      if (this.userId) {\n        this.loadData();\n      }\n    });\n    // Subscribe to auth state changes\n    this.userSubscription = this.supabaseService.currentUser$.subscribe(authUser => {\n      if (!authUser) {\n        console.log('TodayPage: No authenticated user, but not redirecting');\n        // Removed redirect to allow direct access\n        return;\n      }\n      // User is authenticated, get user data\n      this.userId = authUser.id;\n      // Get user data from Supabase\n      this.userService.getUserById(authUser.id).subscribe(userData => {\n        if (!userData) {\n          console.log('TodayPage: No user data found, but not redirecting');\n          // Removed redirect to allow direct access\n          return;\n        }\n        console.log('TodayPage: User data loaded:', userData);\n        this.loadData();\n      });\n    });\n    // Set up user$ observable for template binding\n    this.user$ = this.supabaseService.currentUser$.pipe(switchMap(authUser => {\n      if (!authUser) {\n        return of(null);\n      }\n      return this.userService.getUserById(authUser.id);\n    }));\n    // Subscribe to user$ to get user preferences\n    const userDataSubscription = this.user$.subscribe({\n      next: user => {\n        if (user) {\n          this.showSidequests = user.sidequests_switch;\n          this.currentUser = user;\n        }\n      }\n    });\n    // Add the subscription to be cleaned up\n    this.userSubscription = new Subscription();\n    this.userSubscription.add(userDataSubscription);\n  }\n  ngOnInit() {\n    // Generate week dates and preload data for all days\n    this.generateWeekDates();\n    // Preload data for all days in the week\n    setTimeout(() => {\n      this.preloadWeekData();\n    }, 0);\n    // Load celebration shown dates from localStorage and clean up old ones\n    try {\n      // Get today's date\n      const today = new Date();\n      const todayStr = this.formatDate(today);\n      // First, collect all localStorage keys\n      const allKeys = [];\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i);\n        if (key) {\n          allKeys.push(key);\n        }\n      }\n      // Find and remove all celebration_shown keys except today's\n      allKeys.forEach(key => {\n        if (key.startsWith('celebration_shown_') && key !== `celebration_shown_${todayStr}`) {\n          localStorage.removeItem(key);\n        }\n      });\n      // Check if we have a celebration shown for today\n      const todayCelebrationShown = localStorage.getItem(`celebration_shown_${todayStr}`);\n      // Add to our tracking array if found\n      this.celebrationShownDates = [];\n      if (todayCelebrationShown) {\n        this.celebrationShownDates.push(todayStr);\n      }\n    } catch (error) {\n      this.celebrationShownDates = [];\n    }\n  }\n  ionViewWillEnter() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      // Authentication is now handled by the AuthGuard\n      // Just get the current user and load data\n      const authUser = _this.supabaseService._currentUser.value;\n      if (!authUser) {\n        console.log('TodayPage: No authenticated user, but not redirecting');\n        return;\n      }\n      // Use the UserService to get or create the user document\n      _this.userService.ensureUserExists(authUser).subscribe(userData => {\n        if (!userData) {\n          _this.router.navigateByUrl('/signup');\n          return;\n        }\n        // Get end date\n        let endDate = userData.end_of_current_plan ? new Date(userData.end_of_current_plan) : null;\n        const currentDate = new Date();\n        // Compare dates properly\n        let isValidPlan = false;\n        if (endDate instanceof Date) {\n          isValidPlan = endDate > currentDate;\n        }\n        if (!isValidPlan) {\n          // Prevent multiple redirects\n          if (_this.isRedirecting) return;\n          _this.isRedirecting = true;\n          setTimeout(() => {\n            _this.router.navigateByUrl('/pricing');\n            setTimeout(() => {\n              _this.isRedirecting = false;\n            }, 2000);\n          }, 500);\n          return;\n        }\n        // Check if we have cached data for this date\n        const dateKey = _this.formatDate(_this.selectedDate);\n        if (_this.questCache[dateKey]) {\n          // Use cached data\n          _this.quests = _this.questCache[dateKey];\n          // Initialize slider backgrounds immediately\n          requestAnimationFrame(() => {\n            _this.initializeSliderBackgrounds();\n          });\n          // Load daily side quest if needed\n          _this.loadDailySideQuest();\n        } else {\n          // Load data with the current selected date\n          _this.loadData();\n        }\n      });\n      // Make sure the URL reflects the selected date and week offset\n      const route = _this.router.url;\n      const dateParam = _this.formatDate(_this.selectedDate);\n      if (route === '/today') {\n        // If we're on the base route, update to include the date and week_offset as query parameters\n        _this.router.navigate(['/today'], {\n          queryParams: {\n            date: dateParam,\n            week_offset: _this.weekOffset !== 0 ? _this.weekOffset : null\n          },\n          replaceUrl: true\n        });\n      }\n    })();\n  }\n  // Initialize all slider backgrounds\n  initializeSliderBackgrounds() {\n    // Use requestAnimationFrame for better performance\n    requestAnimationFrame(() => {\n      const sliders = document.querySelectorAll('.progress-slider');\n      if (sliders.length === 0) {\n        return;\n      }\n      sliders.forEach(slider => {\n        if (slider instanceof HTMLInputElement) {\n          // Get the slider's quest ID for debugging\n          const sliderQuestId = slider.getAttribute('data-quest-id');\n          if (!sliderQuestId) {\n            return;\n          }\n          // Get the exact value from the slider (no rounding)\n          const sliderValue = parseInt(slider.value);\n          const minValue = parseInt(slider.min);\n          const maxValue = parseInt(slider.max);\n          // Calculate the percentage value\n          const percentage = maxValue > minValue ? (sliderValue - minValue) / (maxValue - minValue) * 100 : 0;\n          // Set the background directly with hardcoded colors\n          slider.style.background = `linear-gradient(to right, #4169E1 0%, #4169E1 ${percentage}%, #2C2C2E ${percentage}%, #2C2C2E 100%)`;\n          // Add a data attribute to track the current value\n          slider.setAttribute('data-current-value', slider.value);\n        } else if (slider instanceof HTMLElement && slider.tagName === 'ION-RANGE') {\n          // Get the slider's quest ID for debugging\n          const sliderQuestId = slider.getAttribute('data-quest-id');\n          if (!sliderQuestId) {\n            return;\n          }\n          // Get the value from the element's properties or attributes\n          const valueAttr = slider.getAttribute('value') || '0';\n          const minAttr = slider.getAttribute('min') || '0';\n          const maxAttr = slider.getAttribute('max') || '100';\n          const sliderValue = parseInt(valueAttr);\n          const minValue = parseInt(minAttr);\n          const maxValue = parseInt(maxAttr);\n          // Calculate the percentage value\n          const percentage = maxValue > minValue ? (sliderValue - minValue) / (maxValue - minValue) * 100 : 0;\n          // Set the CSS variable for the progress\n          slider.style.setProperty('--progress-value', `${percentage}%`);\n          // Add a data attribute to track the current value\n          slider.setAttribute('data-current-value', sliderValue.toString());\n        }\n      });\n    });\n  }\n  ionViewWillLeave() {\n    console.log('TodayPage: ionViewWillLeave called');\n  }\n  ngOnDestroy() {\n    console.log('TodayPage: ngOnDestroy called');\n    if (this.userSubscription) {\n      this.userSubscription.unsubscribe();\n    }\n  }\n  loadData() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this2.userId) {\n        return;\n      }\n      // Update header text\n      _this2.updateHeaderText();\n      // Check if we have cached data for this date\n      const dateKey = _this2.formatDate(_this2.selectedDate);\n      if (_this2.questCache[dateKey]) {\n        // Use cached data\n        _this2.quests = _this2.questCache[dateKey];\n        // Initialize slider backgrounds immediately\n        requestAnimationFrame(() => {\n          _this2.initializeSliderBackgrounds();\n        });\n        // Load daily side quest if needed\n        _this2.loadDailySideQuest();\n        return;\n      }\n      // Set up date variables\n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n      const selectedDate = new Date(_this2.selectedDate);\n      selectedDate.setHours(0, 0, 0, 0);\n      const isTodaySelected = selectedDate.getTime() === today.getTime();\n      console.log('TodayPage: Loading data for date:', _this2.formatDate(_this2.selectedDate));\n      if (isTodaySelected) {\n        // Check if we've already calculated streaks for today\n        const todayDateString = _this2.formatDate(today);\n        try {\n          const {\n            value: lastStreakCalculation\n          } = yield _this2.preferencesService.get('last_streak_calculation');\n          if (lastStreakCalculation !== todayDateString) {\n            console.log('TodayPage: First time loading today, calculating streaks');\n            // Najprv spracujeme všetky questy pomocou checkMissedDays\n            yield firstValueFrom(_this2.questService.getQuests(_this2.userId).pipe(take(1), switchMap(/*#__PURE__*/function () {\n              var _ref = _asyncToGenerator(function* (quests) {\n                // Check missed days for each quest\n                for (const quest of quests) {\n                  if (quest.id) {\n                    yield _this2.questService.checkMissedDays(quest.id);\n                  }\n                }\n                // Potom vytvoríme progress záznamy pre quit questy\n                yield _this2.questService.createQuitQuestProgressForToday();\n                // NEBUDEME tu nastavovať last_streak_calculation, aby sa mohli vypočítať streaky v ďalšej časti kódu\n                return quests;\n              });\n              return function (_x) {\n                return _ref.apply(this, arguments);\n              };\n            }())));\n            // Streaky sa vypočítajú v ďalšej časti kódu\n          } else {\n            console.log('TodayPage: Streaks already calculated for today');\n          }\n        } catch (error) {\n          console.error('TodayPage: Error checking last streak calculation:', error);\n          // Ak nastane chyba, nenastavujeme last_streak_calculation, aby sa mohli vypočítať streaky\n        }\n        // Recalculate streak for the daily side quest only for today\n        if (_this2.showSidequests) {\n          _this2.sideQuestService.recalculateSideQuestStreak(_this2.userId, _this2.selectedDate).subscribe({\n            error: error => {\n              console.error('Error recalculating side quest streak:', error);\n            }\n          });\n        }\n      }\n      // Load quests\n      _this2.questService.getQuests(_this2.userId).pipe(take(1), switchMap(quests => {\n        // Filter active quests for the selected date\n        const filteredQuests = _this2.filterQuestsForDate(quests, _this2.selectedDate);\n        if (filteredQuests.length === 0) {\n          return of([]);\n        }\n        // Sort filtered quests by creation date (newest first) or ID\n        const sortedFilteredQuests = [...filteredQuests].sort((a, b) => {\n          if (a.created_at && b.created_at) {\n            return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();\n          }\n          return a.id && b.id ? a.id.localeCompare(b.id) : 0;\n        });\n        // Get all progress for all quests at once\n        return _this2.questService.getQuestProgressForDate(_this2.userId, _this2.selectedDate).pipe(take(1), switchMap(allProgress => {\n          // Create a lookup for quick access\n          const progressLookup = {};\n          allProgress.forEach(progress => {\n            progressLookup[progress.quest_id] = progress;\n          });\n          // For today's view, calculate streaks once per day\n          // For other days, just use the streak from the database\n          if (isTodaySelected) {\n            // Check if we've already calculated streaks for today\n            const todayDateString = _this2.formatDate(today);\n            return _this2.preferencesService.get('last_streak_calculation').then(({\n              value: lastStreakCalculation\n            }) => {\n              if (lastStreakCalculation !== todayDateString) {\n                console.log('TodayPage: First time loading today, calculating streaks');\n                // Calculate streaks using our streak calculator\n                return _this2.streakCalculator.calculateStreaks(_this2.userId, sortedFilteredQuests).then(streaks => {\n                  // Map quests with progress and calculated streaks\n                  return sortedFilteredQuests.map(quest => {\n                    const progress = progressLookup[quest.id];\n                    const calculatedStreak = streaks[quest.id] || 0;\n                    // Update the streak in the database\n                    _this2.questService.updateQuestStreak(quest.id, calculatedStreak).subscribe();\n                    return {\n                      ...quest,\n                      completed: (progress === null || progress === void 0 ? void 0 : progress.completed) || false,\n                      value_achieved: (progress === null || progress === void 0 ? void 0 : progress.value_achieved) || 0,\n                      streak: calculatedStreak\n                    };\n                  });\n                }).then(result => {\n                  // Po výpočte streakov nastavíme last_streak_calculation\n                  _this2.preferencesService.set('last_streak_calculation', todayDateString);\n                  return result;\n                });\n              } else {\n                console.log('TodayPage: Streaks already calculated for today, using database values');\n                // Just use the streak from the database\n                return sortedFilteredQuests.map(quest => {\n                  const progress = progressLookup[quest.id];\n                  return {\n                    ...quest,\n                    completed: (progress === null || progress === void 0 ? void 0 : progress.completed) || false,\n                    value_achieved: (progress === null || progress === void 0 ? void 0 : progress.value_achieved) || 0,\n                    streak: quest.streak || 0\n                  };\n                });\n              }\n            }).catch(error => {\n              console.error('TodayPage: Error checking last streak calculation:', error);\n              // If there's an error, just use the streak from the database\n              return sortedFilteredQuests.map(quest => {\n                const progress = progressLookup[quest.id];\n                return {\n                  ...quest,\n                  completed: (progress === null || progress === void 0 ? void 0 : progress.completed) || false,\n                  value_achieved: (progress === null || progress === void 0 ? void 0 : progress.value_achieved) || 0,\n                  streak: quest.streak || 0\n                };\n              });\n            });\n          } else {\n            // For previous days, just use the streak from the database but set it to 0 for display\n            return Promise.resolve(sortedFilteredQuests.map(quest => {\n              const progress = progressLookup[quest.id];\n              return {\n                ...quest,\n                completed: (progress === null || progress === void 0 ? void 0 : progress.completed) || false,\n                value_achieved: (progress === null || progress === void 0 ? void 0 : progress.value_achieved) || 0,\n                streak: 0 // Don't show streak for previous days\n              };\n            }));\n          }\n        }));\n      })).subscribe({\n        next: questsWithProgress => {\n          // Sort quests by creation date (newest first) or ID\n          const sortedQuests = [...questsWithProgress].sort((a, b) => {\n            if (a.created_at && b.created_at) {\n              return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();\n            }\n            return a.id && b.id ? a.id.localeCompare(b.id) : 0;\n          });\n          // Check if all quests are completed for today\n          _this2.checkAllQuestsCompleted(sortedQuests);\n          // Update the quests array\n          _this2.quests = sortedQuests;\n          // Cache the quests for this date\n          const dateKey = _this2.formatDate(_this2.selectedDate);\n          _this2.questCache[dateKey] = sortedQuests;\n          // Update the week date progress\n          _this2.updateWeekDateProgress();\n          // Initialize slider backgrounds\n          requestAnimationFrame(() => {\n            _this2.initializeSliderBackgrounds();\n          });\n          // Load daily side quest if needed\n          _this2.loadDailySideQuest();\n        },\n        error: error => {\n          console.error('Error loading quests:', error);\n        }\n      });\n    })();\n  }\n  generateWeekDates() {\n    const today = new Date();\n    // Calculate the start of the week based on week offset\n    // This starts on Monday (1) instead of Sunday (0)\n    const currentDay = today.getDay(); // 0 = Sunday, 6 = Saturday\n    const daysFromMonday = currentDay === 0 ? 6 : currentDay - 1; // Convert to Monday-based (0 = Monday)\n    const startOfWeek = new Date(today);\n    startOfWeek.setDate(today.getDate() - daysFromMonday + 7 * this.weekOffset);\n    this.weekDates = [];\n    for (let i = 0; i < 7; i++) {\n      const date = new Date(startOfWeek);\n      date.setDate(startOfWeek.getDate() + i);\n      const dateString = this.formatDate(date);\n      const isToday = this.isSameDay(date, today);\n      const isSelected = this.isSameDay(date, this.selectedDate);\n      const isFuture = date > today;\n      // Check if we have cached progress for this date\n      const dateKey = dateString;\n      let totalQuests = 0;\n      let completedQuests = 0;\n      let completionPercentage = 0;\n      if (this.weekProgressCache[dateKey]) {\n        const cached = this.weekProgressCache[dateKey];\n        totalQuests = cached.total;\n        completedQuests = cached.completed;\n        completionPercentage = totalQuests > 0 ? Math.round(completedQuests / totalQuests * 100) : 0;\n      }\n      this.weekDates.push({\n        date: dateString,\n        day: date.getDate(),\n        is_today: isToday,\n        is_selected: isSelected,\n        is_future: isFuture,\n        total_quests: totalQuests,\n        completed_quests: completedQuests,\n        completion_percentage: completionPercentage\n      });\n    }\n    // Preload data for all days in the week\n    if (this.userId) {\n      // Use setTimeout to allow the UI to render first\n      setTimeout(() => {\n        this.preloadWeekData();\n      }, 0);\n    }\n  }\n  updateWeekDateProgress() {\n    if (!this.userId) return;\n    // For each date in the week, update the progress\n    this.weekDates.forEach((weekDate, index) => {\n      if (weekDate.is_future) return;\n      const date = new Date(weekDate.date);\n      const dateKey = this.formatDate(date);\n      // Check if we have cached progress for this date\n      if (this.weekProgressCache[dateKey]) {\n        const cached = this.weekProgressCache[dateKey];\n        this.weekDates[index].total_quests = cached.total;\n        this.weekDates[index].completed_quests = cached.completed;\n        this.weekDates[index].completion_percentage = cached.total > 0 ? Math.round(cached.completed / cached.total * 100) : 0;\n        return;\n      }\n      // If we have cached quests for this date, use them to calculate progress\n      if (this.questCache[dateKey]) {\n        const cachedQuests = this.questCache[dateKey];\n        const totalQuests = cachedQuests.length;\n        const completedQuests = cachedQuests.filter(q => q.completed).length;\n        // Cache the progress\n        this.weekProgressCache[dateKey] = {\n          total: totalQuests,\n          completed: completedQuests\n        };\n        // Update the week date\n        this.weekDates[index].total_quests = totalQuests;\n        this.weekDates[index].completed_quests = completedQuests;\n        this.weekDates[index].completion_percentage = totalQuests > 0 ? Math.round(completedQuests / totalQuests * 100) : 0;\n        return;\n      }\n    });\n    // Preload data for all days in the week\n    this.preloadWeekData();\n  }\n  // Helper method to filter quests for a specific date\n  filterQuestsForDate(quests, date) {\n    const dateObj = new Date(date);\n    const dayOfWeek = dateObj.getDay(); // 0 = Sunday, 1 = Monday, etc.\n    // Django uses Monday=0, Sunday=6 format, so we need to convert\n    const djangoDayOfWeek = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Convert to Django format\n    const dayOfMonth = dateObj.getDate(); // 1-31\n    console.log(`TodayPage: Filtering quests for date ${this.formatDate(date)}, day of week: ${dayOfWeek} (Django: ${djangoDayOfWeek}), day of month: ${dayOfMonth}`);\n    const filteredQuests = quests.filter(quest => {\n      console.log(`TodayPage: Checking quest ${quest.id} (${quest.name}), type: ${quest.quest_type}, period: ${quest.goal_period}, task_days_of_week: ${quest.task_days_of_week}, task_days_of_month: ${quest.task_days_of_month}`);\n      if (!quest.active) {\n        console.log(`TodayPage: Quest ${quest.id} (${quest.name}) is not active, filtering out`);\n        return false;\n      }\n      // Only show quests from the date they were created\n      if (quest.created_at) {\n        const createdDate = new Date(quest.created_at);\n        createdDate.setHours(0, 0, 0, 0);\n        dateObj.setHours(0, 0, 0, 0);\n        // If the selected date is before the quest was created, don't show it\n        if (dateObj < createdDate) {\n          return false;\n        }\n      }\n      // Daily quests are always shown\n      if (quest.goal_period === 'day') {\n        return true;\n      }\n      // Weekly quests are shown on specific days\n      if (quest.goal_period === 'week') {\n        if (!quest.task_days_of_week) {\n          console.log(`TodayPage: Quest ${quest.id} (${quest.name}) has no task_days_of_week specified, showing every day`);\n          return true; // If no days specified, show every day\n        }\n        // Parse task_days_of_week\n        let taskDays = [];\n        if (typeof quest.task_days_of_week === 'string') {\n          taskDays = quest.task_days_of_week.split(',').map(day => day.trim());\n          console.log(`TodayPage: Quest ${quest.id} (${quest.name}) has task_days_of_week as string: ${quest.task_days_of_week}, parsed to:`, taskDays);\n        } else if (Array.isArray(quest.task_days_of_week)) {\n          taskDays = quest.task_days_of_week;\n          console.log(`TodayPage: Quest ${quest.id} (${quest.name}) has task_days_of_week as array:`, taskDays);\n        }\n        // Check if current day is in task days\n        // Convert current day to different formats for comparison\n        const dayNameShort = this.getDayNameShort(djangoDayOfWeek);\n        const dayNameFull = this.getDayNameFull(djangoDayOfWeek);\n        console.log(`TodayPage: Checking if day ${dayNameFull} (${dayNameShort}, ${djangoDayOfWeek}) is in task days:`, taskDays);\n        const isIncluded = taskDays.includes(djangoDayOfWeek) || taskDays.includes(djangoDayOfWeek.toString()) || taskDays.includes(dayNameShort) || taskDays.includes(dayNameFull);\n        console.log(`TodayPage: Quest ${quest.id} (${quest.name}) should be shown on ${dayNameFull}? ${isIncluded}`);\n        return isIncluded;\n      }\n      // Monthly quests are shown on specific days of month\n      if (quest.goal_period === 'month') {\n        if (!quest.task_days_of_month) return true; // If no days specified, show every day\n        // Parse task_days_of_month\n        let taskDays = [];\n        if (typeof quest.task_days_of_month === 'string') {\n          taskDays = quest.task_days_of_month.split(',').map(day => parseInt(day.trim()));\n        } else if (Array.isArray(quest.task_days_of_month)) {\n          taskDays = quest.task_days_of_month;\n        }\n        // Check if current day is in task days\n        return taskDays.includes(dayOfMonth) || taskDays.includes(dayOfMonth.toString());\n      }\n      return false;\n    });\n    console.log(`TodayPage: Filtered ${quests.length} quests to ${filteredQuests.length} for date ${this.formatDate(date)}`);\n    return filteredQuests;\n  }\n  selectDate(dateObj) {\n    if (this.isLoadingData) {\n      return;\n    }\n    const dateData = dateObj.date;\n    this.isLoadingData = true;\n    this.selectedDateData = dateObj;\n    const date = new Date(dateData);\n    this.selectedDate = date;\n    this.weekDates.forEach(weekDate => {\n      weekDate.is_selected = weekDate.date === dateData;\n    });\n    const formattedDate = this.formatDate(date);\n    this.router.navigate(['/today'], {\n      queryParams: {\n        date: formattedDate,\n        week_offset: this.weekOffset !== 0 ? this.weekOffset : null\n      },\n      replaceUrl: true\n    });\n    this.updateHeaderText();\n    setTimeout(() => {\n      this.loadData();\n      this.isLoadingData = false;\n    }, 10);\n  }\n  changeWeek(direction) {\n    // Prevent multiple rapid week changes\n    if (this.isChangingWeek) {\n      return;\n    }\n    this.isChangingWeek = true;\n    // Update the week offset\n    this.weekOffset += direction;\n    // Generate new week dates with the updated offset\n    this.generateWeekDates();\n    // Preload quest data for all days in the week\n    this.preloadWeekData();\n    // Update the URL with the new week offset while preserving the selected date\n    const dateParam = this.formatDate(this.selectedDate);\n    this.router.navigate(['/today'], {\n      queryParams: {\n        date: dateParam,\n        week_offset: this.weekOffset\n      },\n      replaceUrl: true\n    });\n    // Reset the flag after a short delay\n    setTimeout(() => {\n      this.isChangingWeek = false;\n    }, 300);\n  }\n  // Preload data for all days in the current week\n  preloadWeekData() {\n    if (!this.userId) return;\n    // Get all quests once to avoid multiple API calls\n    this.questService.getQuests(this.userId).pipe(take(1)).subscribe(allQuests => {\n      // Create an array of observables for each date\n      const dateObservables = this.weekDates.filter(weekDate => !weekDate.is_future).map(weekDate => {\n        const date = new Date(weekDate.date);\n        const dateKey = this.formatDate(date);\n        // Skip if we already have cached data\n        if (this.weekProgressCache[dateKey]) {\n          return of({\n            date: weekDate.date,\n            progress: this.weekProgressCache[dateKey]\n          });\n        }\n        // Filter active quests for this date\n        const activeQuests = this.filterQuestsForDate(allQuests, date);\n        // If no active quests, return empty progress\n        if (activeQuests.length === 0) {\n          const emptyProgress = {\n            total: 0,\n            completed: 0\n          };\n          this.weekProgressCache[dateKey] = emptyProgress;\n          return of({\n            date: weekDate.date,\n            progress: emptyProgress\n          });\n        }\n        // Get progress for this date\n        return this.questService.getQuestProgressForDate(this.userId, date).pipe(take(1), map(progressList => {\n          // Count completed quests\n          const questIds = activeQuests.map(q => q.id);\n          const relevantProgress = progressList.filter(p => questIds.includes(p.quest_id));\n          const completedQuests = relevantProgress.filter(p => p.completed).length;\n          const totalQuests = activeQuests.length;\n          // Create progress object\n          const progress = {\n            total: totalQuests,\n            completed: completedQuests\n          };\n          // Cache the progress\n          this.weekProgressCache[dateKey] = progress;\n          return {\n            date: weekDate.date,\n            progress\n          };\n        }));\n      });\n      // Process all date observables in parallel\n      forkJoin(dateObservables).subscribe(results => {\n        // Update the week dates with the progress\n        results.forEach(result => {\n          const index = this.weekDates.findIndex(wd => wd.date === result.date);\n          if (index >= 0) {\n            this.weekDates[index].total_quests = result.progress.total;\n            this.weekDates[index].completed_quests = result.progress.completed;\n            this.weekDates[index].completion_percentage = result.progress.total > 0 ? Math.round(result.progress.completed / result.progress.total * 100) : 0;\n          }\n        });\n      });\n    });\n  }\n  updateHeaderText() {\n    const today = new Date();\n    if (this.isSameDay(this.selectedDate, today)) {\n      this.headerText = 'Today';\n    } else if (this.isSameDay(this.selectedDate, new Date(today.setDate(today.getDate() - 1)))) {\n      this.headerText = 'Yesterday';\n    } else if (this.isSameDay(this.selectedDate, new Date(today.setDate(today.getDate() + 2)))) {\n      this.headerText = 'Tomorrow';\n    } else {\n      // Format as \"Mon, 15 Jan\"\n      this.headerText = this.selectedDate.toLocaleDateString('en-US', {\n        weekday: 'short',\n        day: 'numeric',\n        month: 'short'\n      });\n    }\n  }\n  toggleQuest(quest) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this3.userId || !quest.id) return;\n      // Check if this specific quest is already being toggled\n      if (_this3.togglingQuestIds[quest.id]) {\n        console.log(`TodayPage: Quest ${quest.id} (${quest.name}) is already being toggled, ignoring duplicate call`);\n        return;\n      }\n      // Set flag for this specific quest\n      _this3.togglingQuestIds[quest.id] = true;\n      console.log(`TodayPage: Starting toggle for quest ${quest.id} (${quest.name})`);\n      try {\n        // For normal quests, we don't want to toggle the value when clicking on the quest\n        // Instead, we want to keep the current value from the slider\n        // This is different from the original behavior where clicking would toggle between 0 and goal_value\n        // We'll just log that the quest was clicked but not change any values\n        console.log(`TodayPage: Quest ${quest.id} (${quest.name}) clicked, keeping current value: ${quest.value_achieved}`);\n        // No need to update the database since we're not changing any values\n        // Just release the flag and return\n        delete _this3.togglingQuestIds[quest.id];\n        return;\n      } catch (error) {\n        console.error(`TodayPage: Error in toggleQuest for ${quest.id} (${quest.name}):`, error);\n      } finally {\n        // Reset flag for this specific quest\n        delete _this3.togglingQuestIds[quest.id];\n        console.log(`TodayPage: Finished toggle for quest ${quest.id} (${quest.name})`);\n      }\n    })();\n  }\n  updateQuestProgress(quest, event) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this4.userId || !quest.id) return;\n      // Check if this specific quest is already being updated\n      if (_this4.updatingQuestIds[quest.id]) {\n        return;\n      }\n      // Set flag for this specific quest\n      _this4.updatingQuestIds[quest.id] = true;\n      try {\n        // Store the original completed state before any changes\n        const wasCompletedBefore = quest.completed;\n        console.log(`TodayPage: Quest ${quest.id} (${quest.name}) original completed state: ${wasCompletedBefore}`);\n        // Update the slider background if an event is provided\n        if (event) {\n          // Handle both standard Event and Ionic's CustomEvent\n          const slider = event.target || (event.detail ? event.detail.value : null);\n          _this4.updateSliderBackground(slider);\n          // Verify that the slider is for the correct quest\n          const sliderQuestId = slider instanceof HTMLElement ? slider.getAttribute('data-quest-id') : null;\n          if (sliderQuestId && sliderQuestId !== quest.id) {\n            delete _this4.updatingQuestIds[quest.id];\n            return;\n          }\n          // Get the value from the slider\n          let sliderValue = 0;\n          if (event.detail && event.detail.value !== undefined) {\n            // Ionic range event\n            sliderValue = event.detail.value;\n          } else if (slider instanceof HTMLInputElement) {\n            // Standard input event\n            sliderValue = parseInt(slider.value);\n          } else if (slider instanceof HTMLElement && slider.tagName === 'ION-RANGE') {\n            // Ionic range element\n            const valueAttr = slider.getAttribute('value') || '0';\n            sliderValue = parseInt(valueAttr);\n          }\n          // Update the quest's value_achieved with the slider value\n          quest.value_achieved = sliderValue;\n          // Update completed status based on quest type and value\n          // This exactly matches the Django implementation in toggle_quest view\n          if (quest.quest_type === 'build') {\n            // For build quests, completed when value >= goal\n            quest.completed = sliderValue >= quest.goal_value;\n          } else {\n            // 'quit' type\n            // For quit quests, completed when value < goal (opposite of build)\n            quest.completed = sliderValue < quest.goal_value;\n          }\n          console.log(`TodayPage: Quest ${quest.id} (${quest.name}) new completed state: ${quest.completed}`);\n        }\n        // Make a deep copy of the quest to avoid reference issues\n        const questCopy = {\n          ...quest\n        };\n        // Call the service and get the updated values\n        const result = yield _this4.questService.toggleQuestCompletion(_this4.userId, quest.id, _this4.selectedDate, quest.value_achieved, questCopy);\n        // Update the quest in the UI with the returned values\n        quest.completed = result.completed;\n        quest.value_achieved = result.value_achieved;\n        // Get today's date for comparison\n        const today = new Date();\n        today.setHours(0, 0, 0, 0);\n        const selectedDate = new Date(_this4.selectedDate);\n        selectedDate.setHours(0, 0, 0, 0);\n        const isTodaySelected = selectedDate.getTime() === today.getTime();\n        // Handle streak calculation differently based on whether we're in today's view or a previous day\n        if (isTodaySelected) {\n          // For today's view, manually calculate the streak by going backward from today\n          // until we find a non-completed progress entry\n          // Use the streak from the result (from Supabase)\n          let streak = result.streak;\n          // Get the current completed state after the update\n          const isCompletedNow = quest.completed;\n          console.log(`TodayPage: Quest ${quest.id} (${quest.name}) completion status: was ${wasCompletedBefore}, now ${isCompletedNow}`);\n          // Only update streak if the completion status has changed\n          if (wasCompletedBefore !== isCompletedNow) {\n            if (isCompletedNow) {\n              // Changed from incomplete to complete\n              streak++;\n              console.log(`TodayPage: Quest ${quest.id} (${quest.name}) changed from incomplete to complete, streak increased to ${streak}`);\n            } else {\n              // Changed from complete to incomplete\n              streak = Math.max(0, streak - 1);\n              console.log(`TodayPage: Quest ${quest.id} (${quest.name}) changed from complete to incomplete, streak decreased to ${streak}`);\n            }\n            // Update the streak in the database\n            _this4.questService.updateQuestStreak(quest.id, streak).subscribe({\n              next: () => {\n                console.log(`TodayPage: Successfully updated streak for quest ${quest.id} to ${streak}`);\n                // Update the quest in the cache\n                const dateKey = _this4.formatDate(_this4.selectedDate);\n                if (_this4.questCache[dateKey]) {\n                  const cachedQuestIndex = _this4.questCache[dateKey].findIndex(q => q.id === quest.id);\n                  if (cachedQuestIndex >= 0) {\n                    _this4.questCache[dateKey][cachedQuestIndex].streak = streak;\n                  }\n                }\n              },\n              error: error => console.error(`TodayPage: Error updating streak for quest ${quest.id}:`, error)\n            });\n          } else {\n            console.log(`TodayPage: Quest ${quest.id} (${quest.name}) completion status did not change, keeping streak at ${streak}`);\n          }\n        } else {\n          // For previous days, recalculate streak for today\n          console.log(`TodayPage: Quest toggled in previous day (${_this4.formatDate(_this4.selectedDate)}), recalculating streak for today`);\n          // Get the quest details\n          _this4.questService.getQuest(quest.id).subscribe(questDetails => {\n            if (!questDetails) {\n              console.error(`TodayPage: Could not get quest details for ${quest.id}`);\n              return;\n            }\n            // Calculate the streak for today using our streak calculator\n            _this4.streakCalculator.calculateStreak(_this4.userId, quest.id).then(calculatedStreak => {\n              console.log(`TodayPage: Recalculated streak for quest ${quest.id} for today: ${calculatedStreak}`);\n              // Update the streak in the database\n              _this4.questService.updateQuestStreak(quest.id, calculatedStreak).subscribe({\n                next: () => {\n                  console.log(`TodayPage: Successfully updated streak for quest ${quest.id} to ${calculatedStreak}`);\n                  // Clear today's cache for next time\n                  const todayString = _this4.formatDate(today);\n                  console.log('TodayPage: Clearing today\\'s cache to force reload of updated streak next time today is viewed');\n                  delete _this4.questCache[todayString];\n                  // If we have today's date in the week view, update its progress\n                  const todayIndex = _this4.weekDates.findIndex(wd => wd.date === todayString);\n                  if (todayIndex >= 0) {\n                    delete _this4.weekProgressCache[todayString];\n                    _this4.updateProgressRingForDate(todayString);\n                  }\n                },\n                error: error => console.error(`TodayPage: Error updating streak for quest ${quest.id}:`, error)\n              });\n            }).catch(error => {\n              console.error(`TodayPage: Error calculating streak for quest ${quest.id}:`, error);\n            });\n          });\n        }\n        // Update the UI element for this quest\n        _this4.updateQuestUI(quest);\n        // Cache the updated quest data and update progress ring\n        const dateKey = _this4.formatDate(_this4.selectedDate);\n        if (_this4.questCache[dateKey]) {\n          // Find and update the quest in the cache\n          const cachedQuestIndex = _this4.questCache[dateKey].findIndex(q => q.id === quest.id);\n          if (cachedQuestIndex >= 0) {\n            _this4.questCache[dateKey][cachedQuestIndex] = {\n              ...quest\n            };\n          }\n        }\n        // Clear the cache for this date to force a refresh\n        delete _this4.weekProgressCache[dateKey];\n        // Update the progress ring for this date\n        _this4.updateProgressRingForDate(dateKey);\n        // Check if all quests are completed\n        _this4.checkAllQuestsCompleted(_this4.quests);\n      } catch (error) {\n        console.error(`TodayPage: Error updating quest progress:`, error);\n      } finally {\n        // Reset flag for this specific quest\n        delete _this4.updatingQuestIds[quest.id];\n      }\n    })();\n  }\n  // Helper method to update the progress ring for a specific date\n  updateProgressRingForDate(dateKey) {\n    // Find the index of this date in weekDates\n    const index = this.weekDates.findIndex(wd => wd.date === dateKey);\n    if (index < 0) return;\n    // If we have cached quests for this date, use them to calculate progress\n    if (this.questCache[dateKey]) {\n      const cachedQuests = this.questCache[dateKey];\n      const totalQuests = cachedQuests.length;\n      const completedQuests = cachedQuests.filter(q => q.completed).length;\n      // Cache the progress\n      this.weekProgressCache[dateKey] = {\n        total: totalQuests,\n        completed: completedQuests\n      };\n      // Update the week date\n      this.weekDates[index].total_quests = totalQuests;\n      this.weekDates[index].completed_quests = completedQuests;\n      this.weekDates[index].completion_percentage = totalQuests > 0 ? Math.round(completedQuests / totalQuests * 100) : 0;\n      return;\n    }\n    // If no cached quests, fetch from server\n    if (this.userId) {\n      const date = new Date(dateKey);\n      this.questService.getQuestProgressForDate(this.userId, date).pipe(take(1)).subscribe(progressList => {\n        this.questService.getQuests(this.userId).pipe(take(1)).subscribe(quests => {\n          // Filter active quests for this date\n          const activeQuests = this.filterQuestsForDate(quests, date);\n          // Count completed quests\n          const questIds = activeQuests.map(q => q.id);\n          const relevantProgress = progressList.filter(p => questIds.includes(p.quest_id));\n          const completedQuests = relevantProgress.filter(p => p.completed).length;\n          const totalQuests = activeQuests.length;\n          // Cache the progress\n          this.weekProgressCache[dateKey] = {\n            total: totalQuests,\n            completed: completedQuests\n          };\n          // Update the week date\n          this.weekDates[index].total_quests = totalQuests;\n          this.weekDates[index].completed_quests = completedQuests;\n          this.weekDates[index].completion_percentage = totalQuests > 0 ? Math.round(completedQuests / totalQuests * 100) : 0;\n        });\n      });\n    }\n  }\n  // Helper method to update the UI for a specific quest\n  updateQuestUI(quest) {\n    // Find the quest element in the DOM\n    const questElement = document.querySelector(`[data-quest-id=\"${quest.id}\"]`);\n    if (!questElement) {\n      console.error(`TodayPage: Could not find quest element for ID: ${quest.id}`);\n      return;\n    }\n    // Update the completed class\n    if (quest.completed) {\n      questElement.classList.add('completed');\n    } else {\n      questElement.classList.remove('completed');\n    }\n    // Update the streak display - only show streak for today\n    const streakElements = questElement.querySelectorAll('.quest-streak');\n    if (streakElements && streakElements.length > 0) {\n      // Get today's date for comparison\n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n      const selectedDate = new Date(this.selectedDate);\n      selectedDate.setHours(0, 0, 0, 0);\n      const isTodaySelected = selectedDate.getTime() === today.getTime();\n      // Only show streak for today's view\n      if (isTodaySelected) {\n        const streakValue = quest.streak || 0;\n        console.log(`TodayPage: Quest ${quest.id}, completed: ${quest.completed}, streak: ${streakValue}`);\n        // Update all streak elements (there might be multiple due to ngIf)\n        streakElements.forEach(element => {\n          if (element.parentElement && element.parentElement.contains(element)) {\n            // Make sure the streak is visible\n            element.style.display = 'block';\n            element.textContent = `🔥${streakValue}d`;\n          }\n        });\n      } else {\n        // Hide streak for previous days\n        streakElements.forEach(element => {\n          if (element.parentElement && element.parentElement.contains(element)) {\n            element.style.display = 'none';\n            element.textContent = '';\n          }\n        });\n      }\n    }\n    // Update the progress text\n    const progressText = questElement.querySelector('.progress-text');\n    if (progressText) {\n      var _progressText$parentE;\n      const isTimeUnit = (_progressText$parentE = progressText.parentElement) === null || _progressText$parentE === void 0 ? void 0 : _progressText$parentE.classList.contains('progress-time');\n      const unitSuffix = isTimeUnit ? 'm' : '';\n      const goalUnitSuffix = quest.goal_unit !== 'count' && !isTimeUnit ? ` ${quest.goal_unit}` : '';\n      progressText.textContent = `${quest.value_achieved}${unitSuffix}/${quest.goal_value}${unitSuffix}${goalUnitSuffix}`;\n    }\n    console.log(`TodayPage: Updated UI for quest ${quest.id}`);\n  }\n  // Update slider background based on value\n  updateSliderBackground(slider) {\n    if (!slider) {\n      return;\n    }\n    // Handle different types of slider elements\n    let sliderElement;\n    let sliderValue = 0;\n    let minValue = 0;\n    let maxValue = 100;\n    let sliderQuestId = '';\n    if (slider instanceof HTMLInputElement) {\n      // Standard HTML input range\n      sliderElement = slider;\n      sliderQuestId = slider.getAttribute('data-quest-id') || '';\n      sliderValue = parseInt(slider.value);\n      minValue = parseInt(slider.min);\n      maxValue = parseInt(slider.max);\n    } else if (slider instanceof HTMLElement && slider.tagName === 'ION-RANGE') {\n      // Ionic range element\n      sliderElement = slider;\n      sliderQuestId = slider.getAttribute('data-quest-id') || '';\n      // Get the value from the element's properties or attributes\n      const valueAttr = slider.getAttribute('value') || '0';\n      const minAttr = slider.getAttribute('min') || '0';\n      const maxAttr = slider.getAttribute('max') || '100';\n      sliderValue = parseInt(valueAttr);\n      minValue = parseInt(minAttr);\n      maxValue = parseInt(maxAttr);\n    } else {\n      return;\n    }\n    if (!sliderQuestId) {\n      return;\n    }\n    // Calculate the percentage value\n    const percentage = maxValue > minValue ? (sliderValue - minValue) / (maxValue - minValue) * 100 : 0;\n    // For Ionic range, we need to set the CSS variable\n    if (sliderElement.tagName === 'ION-RANGE') {\n      sliderElement.style.setProperty('--progress-value', `${percentage}%`);\n    } else {\n      // Set the background directly with hardcoded colors for standard HTML input\n      sliderElement.style.background = `linear-gradient(to right, #4169E1 0%, #4169E1 ${percentage}%, #2C2C2E ${percentage}%, #2C2C2E 100%)`;\n    }\n    // Add a data attribute to track the current value\n    sliderElement.setAttribute('data-current-value', sliderValue.toString());\n  }\n  /**\n   * Toggle side quest completion\n   * This matches the Django implementation in toggle_daily_side_quest view\n   * Side quests are always toggled between 0 and goal value\n   */\n  toggleSideQuest(sideQuest) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this5.userId || !sideQuest.id) return;\n      // Check if this specific side quest is already being toggled\n      if (_this5.togglingSideQuestIds[sideQuest.id]) {\n        console.log(`TodayPage: Side quest ${sideQuest.id} is already being toggled, ignoring duplicate call`);\n        return;\n      }\n      // Set flag for this specific side quest\n      _this5.togglingSideQuestIds[sideQuest.id] = true;\n      console.log(`TodayPage: Starting toggle for side quest ${sideQuest.id}`);\n      try {\n        // For side quests, we always toggle between 0 and goal value\n        // This matches the Django implementation where side quests are either completed or not\n        console.log(`TodayPage: Click event on side quest ${sideQuest.id}`);\n        // Toggle the value immediately for better UI feedback\n        const newValue = sideQuest.value_achieved === 0 ? sideQuest.current_quest.goal_value : 0;\n        const newCompletedState = newValue === sideQuest.current_quest.goal_value;\n        // Update local state first for immediate feedback\n        sideQuest.value_achieved = newValue;\n        sideQuest.completed = newCompletedState;\n        console.log(`TodayPage: Updated side quest ${sideQuest.id} value to ${sideQuest.value_achieved}, completed: ${sideQuest.completed}`);\n        // Get today's date for comparison\n        const today = new Date();\n        today.setHours(0, 0, 0, 0);\n        const selectedDate = new Date(_this5.selectedDate);\n        selectedDate.setHours(0, 0, 0, 0);\n        const isToday = selectedDate.getTime() === today.getTime();\n        // Only allow toggling side quests for today\n        if (!isToday) {\n          console.log(`TodayPage: Cannot toggle side quest for past date: ${_this5.formatDate(_this5.selectedDate)}`);\n          delete _this5.togglingSideQuestIds[sideQuest.id];\n          return;\n        }\n        // Update the UI element immediately for better feedback\n        _this5.updateSideQuestUI(sideQuest);\n        try {\n          const result = yield _this5.sideQuestService.toggleSideQuestCompletion(sideQuest.id, _this5.userId, _this5.selectedDate // Pass the selected date\n          );\n          console.log(`TodayPage: Successfully toggled side quest ${sideQuest.id}`);\n          console.log(`TodayPage: Updated values:`, result);\n          // Update the side quest in the UI with the returned values from the server\n          sideQuest.completed = result.completed;\n          sideQuest.value_achieved = result.value_achieved;\n          sideQuest.streak = result.streak;\n          // Update the UI element with the updated streak\n          _this5.updateSideQuestUI(sideQuest);\n          // Update the week date progress for the selected date\n          // Clear the cache for this date to force a refresh\n          const dateKey = _this5.formatDate(_this5.selectedDate);\n          delete _this5.weekProgressCache[dateKey];\n          // Update the progress ring for this date\n          _this5.updateProgressRingForDate(dateKey);\n          // Check if all quests are completed\n          _this5.checkAllQuestsCompleted(_this5.quests);\n          // Reset flag for this specific side quest\n          delete _this5.togglingSideQuestIds[sideQuest.id];\n          console.log(`TodayPage: Finished toggle for side quest ${sideQuest.id}`);\n        } catch (error) {\n          console.error(`TodayPage: Error toggling side quest ${sideQuest.id}:`, error);\n          // Revert the local state if the server update failed\n          sideQuest.value_achieved = sideQuest.value_achieved === 0 ? sideQuest.current_quest.goal_value : 0;\n          sideQuest.completed = sideQuest.value_achieved === sideQuest.current_quest.goal_value;\n          _this5.updateSideQuestUI(sideQuest);\n          delete _this5.togglingSideQuestIds[sideQuest.id];\n        }\n      } catch (error) {\n        console.error(`TodayPage: Error in toggleSideQuest for ${sideQuest.id}:`, error);\n        delete _this5.togglingSideQuestIds[sideQuest.id];\n      }\n    })();\n  }\n  // Helper method to update the UI for a specific side quest\n  updateSideQuestUI(sideQuest) {\n    // Find the side quest element in the DOM\n    const questElement = document.querySelector(`.daily-side-quest [data-quest-id=\"${sideQuest.id}\"]`);\n    if (!questElement) {\n      console.error(`TodayPage: Could not find side quest element for ID: ${sideQuest.id}`);\n      return;\n    }\n    // Update the completed class\n    if (sideQuest.completed) {\n      questElement.classList.add('completed');\n    } else {\n      questElement.classList.remove('completed');\n    }\n    // Update the streak display - only show streak for today\n    const streakElements = questElement.querySelectorAll('.quest-streak');\n    if (streakElements && streakElements.length > 0) {\n      // Get today's date for comparison\n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n      const selectedDate = new Date(this.selectedDate);\n      selectedDate.setHours(0, 0, 0, 0);\n      const isTodaySelected = selectedDate.getTime() === today.getTime();\n      // Only show streak for today's view\n      if (isTodaySelected) {\n        const streakValue = sideQuest.streak || 0;\n        console.log(`TodayPage: Side quest ${sideQuest.id}, completed: ${sideQuest.completed}, streak: ${streakValue}`);\n        // Update all streak elements (there might be multiple due to ngIf)\n        streakElements.forEach(element => {\n          if (element.parentElement && element.parentElement.contains(element)) {\n            // Make sure the streak is visible\n            element.style.display = 'block';\n            element.textContent = `🔥${streakValue}d`;\n          }\n        });\n      } else {\n        // Hide streak for previous days\n        streakElements.forEach(element => {\n          if (element.parentElement && element.parentElement.contains(element)) {\n            element.style.display = 'none';\n            element.textContent = '';\n          }\n        });\n      }\n    }\n    // Update the progress text\n    const progressText = questElement.querySelector('.progress-text');\n    if (progressText) {\n      const goalUnit = sideQuest.current_quest.goal_unit !== 'count' ? ` ${sideQuest.current_quest.goal_unit}` : '';\n      progressText.textContent = `${sideQuest.value_achieved}/${sideQuest.current_quest.goal_value}${goalUnit}`;\n    }\n    // Force a repaint to ensure the UI updates\n    setTimeout(() => {\n      if (questElement.parentElement) {\n        const display = questElement.parentElement.style.display;\n        questElement.parentElement.style.display = 'none';\n        // Force a reflow\n        void questElement.parentElement.offsetHeight;\n        questElement.parentElement.style.display = display;\n      }\n    }, 0);\n    console.log(`TodayPage: Updated UI for side quest ${sideQuest.id}`);\n  }\n  openAddQuestModal(event) {\n    event.preventDefault();\n    this.showAddQuestModal = true;\n    this.newQuest = this.getEmptyQuest();\n    this.selectedDaysOfWeek = [];\n    this.selectedDaysOfMonth = [];\n    // Reset hasHighPriorityQuest flag\n    this.hasHighPriorityQuest = false;\n  }\n  closeAddQuestModal() {\n    this.showAddQuestModal = false;\n    // Reset form state\n    this.newQuest = this.getEmptyQuest();\n    this.selectedDaysOfWeek = [];\n    this.selectedDaysOfMonth = [];\n    this.hasHighPriorityQuest = false;\n  }\n  createQuest() {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this6.userId || !_this6.newQuest.name || !_this6.newQuest.emoji || !_this6.newQuest.quest_type || !_this6.newQuest.category || !_this6.newQuest.goal_value || !_this6.newQuest.goal_unit || !_this6.newQuest.goal_period) {\n        console.error('TodayPage: Cannot create quest - missing required fields');\n        return;\n      }\n      try {\n        // Prepare days of week/month based on period\n        if (_this6.newQuest.goal_period === 'week' && _this6.selectedDaysOfWeek.length > 0) {\n          _this6.newQuest.task_days_of_week = _this6.selectedDaysOfWeek.join(',');\n        } else if (_this6.newQuest.goal_period === 'month' && _this6.selectedDaysOfMonth.length > 0) {\n          _this6.newQuest.task_days_of_month = _this6.selectedDaysOfMonth.join(',');\n        }\n        // First check if the user profile exists\n        console.log('TodayPage: Checking if user profile exists for ID:', _this6.userId);\n        const {\n          data: userProfile,\n          error: userError\n        } = yield _this6.supabaseService.getClient().from('profiles').select('id').eq('id', _this6.userId).single();\n        if (userError || !userProfile) {\n          console.error('TodayPage: User profile not found:', userError || 'No profile found');\n          throw new Error('User profile not found. Please ensure you are logged in.');\n        }\n        console.log('TodayPage: Found user profile:', userProfile);\n        // Add user_id and active status\n        // Note: user_id in the quest references profiles.id\n        const questToCreate = {\n          name: _this6.newQuest.name || '',\n          description: _this6.newQuest.description || '',\n          quest_type: _this6.newQuest.quest_type || 'build',\n          goal_value: _this6.newQuest.goal_value || 1,\n          goal_unit: _this6.newQuest.goal_unit || 'count',\n          goal_period: _this6.newQuest.goal_period || 'day',\n          priority: _this6.newQuest.priority || 'basic',\n          category: _this6.newQuest.category || 'strength',\n          emoji: _this6.newQuest.emoji || '🎯',\n          task_days_of_week: _this6.newQuest.task_days_of_week || '',\n          task_days_of_month: _this6.newQuest.task_days_of_month || '',\n          user_id: _this6.userId,\n          // This is profiles.id\n          active: true\n        };\n        console.log('TodayPage: Creating quest:', questToCreate);\n        try {\n          const questId = yield _this6.questService.createQuest(questToCreate);\n          console.log('TodayPage: Quest created successfully with ID:', questId);\n          // For quit quests, create initial progress with completed=true and value_achieved=0\n          if (_this6.newQuest.quest_type === 'quit') {\n            console.log('TodayPage: Creating initial progress for quit quest with completed=true');\n            // Create initial progress for today\n            yield _this6.questService.toggleQuestCompletion(_this6.userId, questId, new Date(),\n            // Today\n            0,\n            // value_achieved = 0 for quit quests\n            {\n              ...questToCreate,\n              id: questId\n            });\n          }\n          // Clear cache for the current date to force a refresh\n          const dateKey = _this6.formatDate(_this6.selectedDate);\n          delete _this6.questCache[dateKey];\n          delete _this6.weekProgressCache[dateKey];\n          console.log('TodayPage: Cleared cache for date:', dateKey);\n          _this6.closeAddQuestModal();\n          _this6.loadData();\n        } catch (questError) {\n          console.error('TodayPage: Error creating quest:', questError);\n          // Check if this is a foreign key constraint error\n          if (questError.message && questError.message.includes('foreign key constraint')) {\n            alert('Database configuration issue detected. Please run the fix_quest_constraints.sql script in the Supabase SQL Editor to fix the foreign key constraints.');\n          } else if (questError.message && questError.message.includes('fix_quest_constraints.sql')) {\n            alert(questError.message);\n          } else {\n            alert(`Error creating quest: ${questError.message}`);\n          }\n        }\n      } catch (error) {\n        console.error('TodayPage: Error in createQuest:', error);\n        alert(`Error: ${error.message || 'Unknown error occurred'}`);\n      }\n    })();\n  }\n  updateDaysOfWeek(event, day) {\n    // Handle both standard Event and Ionic's CustomEvent\n    let isChecked = false;\n    if (event.detail !== undefined) {\n      // Ionic checkbox event\n      isChecked = event.detail.checked;\n    } else if (event.target instanceof HTMLInputElement) {\n      // Standard checkbox event\n      isChecked = event.target.checked;\n    }\n    if (isChecked) {\n      this.selectedDaysOfWeek.push(day);\n    } else {\n      const index = this.selectedDaysOfWeek.indexOf(day);\n      if (index !== -1) {\n        this.selectedDaysOfWeek.splice(index, 1);\n      }\n    }\n    console.log(`TodayPage: Updated days of week: ${this.selectedDaysOfWeek.join(', ')}`);\n  }\n  updateDaysOfMonth(event, day) {\n    // Handle both standard Event and Ionic's CustomEvent\n    let isChecked = false;\n    if (event.detail !== undefined) {\n      // Ionic checkbox event\n      isChecked = event.detail.checked;\n    } else if (event.target instanceof HTMLInputElement) {\n      // Standard checkbox event\n      isChecked = event.target.checked;\n    }\n    if (isChecked) {\n      this.selectedDaysOfMonth.push(day);\n    } else {\n      const index = this.selectedDaysOfMonth.indexOf(day);\n      if (index !== -1) {\n        this.selectedDaysOfMonth.splice(index, 1);\n      }\n    }\n    console.log(`TodayPage: Updated days of month: ${this.selectedDaysOfMonth.join(', ')}`);\n  }\n  updatePeriodDisplay() {\n    // Reset selections when period changes\n    this.selectedDaysOfWeek = [];\n    this.selectedDaysOfMonth = [];\n    console.log(`TodayPage: Period changed to ${this.newQuest.goal_period}, reset selections`);\n  }\n  checkCategoryPriority(event) {\n    if (!this.userId || !this.newQuest.category) return;\n    // If this is an Ionic event, make sure we have the latest category value\n    if (event && event.detail) {\n      this.newQuest.category = event.detail.value;\n      console.log(`TodayPage: Category changed to ${this.newQuest.category} via Ionic event`);\n    }\n    // Check if user already has a high priority quest in this category\n    this.questService.getQuests(this.userId).pipe(take(1), map(quests => {\n      return quests.some(q => q.category === this.newQuest.category && q.priority === 'high' && q.active);\n    })).subscribe({\n      next: hasHighPriority => {\n        this.hasHighPriorityQuest = hasHighPriority;\n        // If user already has a high priority quest, set this one to basic\n        if (hasHighPriority) {\n          this.newQuest.priority = 'basic';\n        }\n        console.log(`TodayPage: Category ${this.newQuest.category} has high priority quest: ${hasHighPriority}`);\n      }\n    });\n  }\n  /**\n   * Check if all quests are completed for today and show celebration if enabled\n   */\n  checkAllQuestsCompleted(quests) {\n    // Only check for today's date\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    const selectedDate = new Date(this.selectedDate);\n    selectedDate.setHours(0, 0, 0, 0);\n    const isTodaySelected = selectedDate.getTime() === today.getTime();\n    const todayStr = this.formatDate(today);\n    if (!isTodaySelected || !this.currentUser) {\n      return;\n    }\n    // Check if celebration has already been shown for today\n    const celebrationShown = localStorage.getItem(`celebration_shown_${todayStr}`);\n    if (celebrationShown) {\n      console.log('TodayPage: Celebration already shown for today:', todayStr);\n      return;\n    }\n    // Check if all quests are completed\n    const allQuestsCompleted = quests.length > 0 && quests.every(quest => quest.completed);\n    // Check if side quest is completed (if enabled)\n    const sideQuestCompleted = !this.showSidequests || !this.dailyQuest || this.dailyQuest.completed;\n    // Show celebration if all quests and side quests are completed and celebration is enabled\n    if (allQuestsCompleted && sideQuestCompleted && this.currentUser.show_celebration) {\n      // Make sure we have the latest user data\n      this.userService.getUserById(this.userId).subscribe(userData => {\n        if (userData) {\n          this.currentUser = userData;\n        }\n        // Show the celebration\n        this.showCelebration = true;\n        // Save today's date to localStorage\n        localStorage.setItem(`celebration_shown_${todayStr}`, 'true');\n        // Update our tracking array\n        if (!this.celebrationShownDates.includes(todayStr)) {\n          this.celebrationShownDates.push(todayStr);\n        }\n      });\n    }\n  }\n  /**\n   * Close the celebration modal\n   */\n  closeCelebration() {\n    this.showCelebration = false;\n  }\n  // Helper methods\n  formatDate(date) {\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n  isSameDay(date1, date2) {\n    return date1.getFullYear() === date2.getFullYear() && date1.getMonth() === date2.getMonth() && date1.getDate() === date2.getDate();\n  }\n  getToday() {\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    return today;\n  }\n  // Convert Django day index (0=Monday, 6=Sunday) to short day name\n  getDayNameShort(djangoDayIndex) {\n    // Map Django day index to day name\n    const dayMap = ['Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa', 'Su'];\n    return dayMap[djangoDayIndex];\n  }\n  // Convert Django day index (0=Monday, 6=Sunday) to full day name\n  getDayNameFull(djangoDayIndex) {\n    // Map Django day index to full day name\n    const dayMap = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];\n    return dayMap[djangoDayIndex];\n  }\n  getEmptyQuest() {\n    return {\n      name: '',\n      description: '',\n      quest_type: 'build',\n      goal_value: 1,\n      goal_unit: 'count',\n      goal_period: 'day',\n      priority: 'basic',\n      // Default to basic priority\n      category: '',\n      emoji: '🎯'\n    };\n  }\n}\n_TodayPage = TodayPage;\n_TodayPage.ɵfac = function TodayPage_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _TodayPage)();\n};\n_TodayPage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _TodayPage,\n  selectors: [[\"app-today\"]],\n  decls: 35,\n  vars: 13,\n  consts: [[\"questForm\", \"ngForm\"], [1, \"ion-padding\", 3, \"fullscreen\"], [1, \"background-container\"], [1, \"gradient-bg\"], [1, \"celestial-body\"], [1, \"ion-no-border\"], [3, \"headerText\"], [1, \"week-row\", \"ion-padding-top\"], [\"class\", \"day-container\", 4, \"ngFor\", \"ngForOf\"], [1, \"ion-justify-content-center\"], [1, \"heartbeat-circle\", \"gradient-text\"], [1, \"add-quest\"], [\"fill\", \"clear\", \"id\", \"add-quest-btn\", 1, \"add-quest-btn\", 3, \"click\"], [1, \"quests\"], [\"class\", \"ion-text-center no-quest-card\", 4, \"ngif\"], [\"class\", \"quest-item ion-margin-bottom\", 3, \"completed\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"ion-padding-top\"], [\"class\", \"quest-item\", 3, \"completed\", \"click\", 4, \"ngIf\"], [\"class\", \"quest-item\", 4, \"ngIf\"], [1, \"add-quest-modal\", 3, \"ionModalDidDismiss\", \"isOpen\", \"backdropDismiss\", \"breakpoints\", \"initialBreakpoint\"], [3, \"user\", \"date\", \"close\", 4, \"ngIf\"], [1, \"day-container\"], [1, \"day-name\"], [1, \"date\", 3, \"click\"], [\"viewBox\", \"0 0 36 36\", 1, \"date-progress\"], [\"cx\", \"18\", \"cy\", \"18\", \"r\", \"13\", \"stroke-dasharray\", \"81.68, 81.68\", 1, \"background-circle\"], [\"cx\", \"18\", \"cy\", \"18\", \"r\", \"13\", \"class\", \"progress-circle\", 3, \"low\", 4, \"ngIf\"], [\"cx\", \"18\", \"cy\", \"18\", \"r\", \"13\", 1, \"progress-circle\"], [1, \"ion-text-center\", \"no-quest-card\"], [\"size\", \"8\"], [\"size\", \"4\"], [\"name\", \"warning\"], [1, \"quest-item\", \"ion-margin-bottom\", 3, \"click\"], [\"size\", \"2\"], [1, \"quest-icon\"], [\"size\", \"8\", 1, \"quest-info\"], [1, \"progress-container\"], [\"class\", \"progress-time\", 4, \"ngIf\"], [\"class\", \"progress\", 4, \"ngIf\"], [\"class\", \"quest-streak\", 4, \"ngIf\"], [1, \"progress-time\"], [\"min\", \"0\", \"snaps\", \"true\", \"ticks\", \"false\", \"snaps-per-step\", \"true\", 1, \"progress-slider\", 3, \"ngModelChange\", \"ionChange\", \"ionInput\", \"max\", \"ngModel\", \"step\"], [1, \"progress-text\"], [1, \"progress\"], [4, \"ngIf\"], [1, \"quest-streak\"], [1, \"quest-item\", 3, \"click\"], [1, \"quest-description\"], [1, \"quest-item\"], [1, \"modal-header\"], [1, \"modal-title\"], [1, \"title-content\"], [1, \"title-icon\"], [\"slot\", \"end\"], [\"fill\", \"clear\", 1, \"close-button\", 3, \"click\"], [\"name\", \"close\", \"size\", \"large\"], [1, \"modal-content\"], [1, \"modal-body\"], [1, \"quest-form\", 3, \"ngSubmit\"], [1, \"form-section\"], [1, \"section-header\"], [1, \"section-divider\"], [1, \"quest-identity-row\"], [1, \"emoji-container\"], [\"type\", \"text\", \"id\", \"emoji\", \"name\", \"emoji\", \"value\", \"\\uD83C\\uDFAF\", \"appEmojiInput\", \"\", \"placeholder\", \"\\uD83C\\uDFAF\", \"required\", \"\", 1, \"emoji-input\", 3, \"ngModelChange\", \"ngModel\"], [1, \"emoji-label\"], [1, \"name-container\"], [\"type\", \"text\", \"id\", \"name\", \"name\", \"name\", \"placeholder\", \" \", \"required\", \"\", 1, \"name-input\", 3, \"ngModelChange\", \"ngModel\"], [1, \"floating-label\"], [1, \"input-group\"], [\"id\", \"description\", \"name\", \"description\", \"placeholder\", \" \", \"rows\", \"3\", 1, \"description-input\", 3, \"ngModelChange\", \"ngModel\"], [\"id\", \"quest_type\", \"name\", \"quest_type\", \"interface\", \"popover\", \"placeholder\", \" \", \"required\", \"\", 1, \"modern-select\", 3, \"ngModelChange\", \"ngModel\"], [\"value\", \"build\"], [\"value\", \"quit\"], [\"id\", \"category\", \"name\", \"category\", \"required\", \"\", \"interface\", \"popover\", \"placeholder\", \" \", 1, \"modern-select\", 3, \"ngModelChange\", \"ionChange\", \"ngModel\"], [\"value\", \"\"], [\"value\", \"strength\"], [\"value\", \"money\"], [\"value\", \"health\"], [\"value\", \"knowledge\"], [\"class\", \"input-group\", 4, \"ngIf\"], [1, \"goal-row\"], [1, \"goal-value-container\"], [\"type\", \"number\", \"id\", \"goal_value\", \"name\", \"goal_value\", \"value\", \"1\", \"min\", \"1\", \"placeholder\", \" \", \"required\", \"\", 1, \"goal-input\", 3, \"ngModelChange\", \"ngModel\"], [1, \"goal-unit-container\"], [\"id\", \"goal_unit\", \"name\", \"goal_unit\", \"interface\", \"popover\", \"placeholder\", \" \", \"required\", \"\", 1, \"modern-select\", \"unit-select\", 3, \"ngModelChange\", \"ngModel\"], [\"value\", \"count\"], [\"value\", \"steps\"], [\"value\", \"m\"], [\"value\", \"km\"], [\"value\", \"sec\"], [\"value\", \"min\"], [\"value\", \"hr\"], [\"value\", \"Cal\"], [\"value\", \"g\"], [\"value\", \"mg\"], [\"value\", \"l\"], [\"value\", \"drink\"], [\"value\", \"pages\"], [\"value\", \"books\"], [\"value\", \"%\"], [\"value\", \"\\u20AC\"], [\"value\", \"$\"], [\"value\", \"\\u00A3\"], [\"id\", \"goal_period\", \"name\", \"goal_period\", \"interface\", \"popover\", \"placeholder\", \" \", \"required\", \"\", 1, \"modern-select\", 3, \"ngModelChange\", \"ionChange\", \"ngModel\"], [\"value\", \"day\"], [\"value\", \"week\"], [\"value\", \"month\"], [\"class\", \"schedule-section\", 4, \"ngIf\"], [1, \"modal-footer\"], [1, \"footer-buttons\"], [\"fill\", \"clear\", 1, \"cancel-btn\", 3, \"click\"], [\"type\", \"submit\", 1, \"create-quest-btn\", 3, \"click\", \"disabled\"], [\"name\", \"add\", \"slot\", \"start\"], [\"id\", \"priority\", \"name\", \"priority\", \"interface\", \"popover\", \"placeholder\", \" \", 1, \"modern-select\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"value\", \"basic\"], [\"value\", \"high\"], [\"class\", \"priority-warning\", 4, \"ngIf\"], [1, \"priority-warning\"], [\"name\", \"warning\", \"color\", \"warning\"], [1, \"schedule-section\"], [1, \"days-grid\"], [\"class\", \"day-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"day-item\"], [\"name\", \"days_of_week\", 1, \"modern-checkbox\", 3, \"ionChange\", \"id\", \"value\"], [1, \"day-label\", 3, \"for\"], [1, \"month-days-grid\"], [\"class\", \"month-day-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"month-day-item\"], [\"name\", \"days_of_month\", 1, \"modern-checkbox\", 3, \"ionChange\", \"id\", \"value\"], [1, \"month-day-label\", 3, \"for\"], [3, \"close\", \"user\", \"date\"]],\n  template: function TodayPage_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"ion-content\", 1)(1, \"div\", 2);\n      i0.ɵɵelement(2, \"div\", 3)(3, \"div\", 4);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(4, \"ion-header\", 5)(5, \"ion-toolbar\");\n      i0.ɵɵelement(6, \"app-header\", 6);\n      i0.ɵɵelementStart(7, \"ion-row\", 7);\n      i0.ɵɵtemplate(8, TodayPage_div_8_Template, 7, 13, \"div\", 8);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(9, \"ion-grid\")(10, \"ion-row\", 9);\n      i0.ɵɵelement(11, \"div\", 10);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(12, \"ion-row\", 11)(13, \"ion-col\")(14, \"h2\");\n      i0.ɵɵtext(15, \"Quests\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(16, \"ion-col\")(17, \"ion-button\", 12);\n      i0.ɵɵlistener(\"click\", function TodayPage_Template_ion_button_click_17_listener($event) {\n        return ctx.openAddQuestModal($event);\n      });\n      i0.ɵɵtext(18, \" + Add Quest \");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(19, \"ion-row\", 13)(20, \"ion-col\");\n      i0.ɵɵtemplate(21, TodayPage_ion_card_21_Template, 11, 0, \"ion-card\", 14)(22, TodayPage_ion_card_22_Template, 15, 10, \"ion-card\", 15);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(23, \"ion-row\", 16)(24, \"ion-col\")(25, \"h2\");\n      i0.ɵɵtext(26, \"Daily Side Quest\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(27, \"ion-row\", 13)(28, \"ion-col\");\n      i0.ɵɵtemplate(29, TodayPage_ion_card_29_Template, 12, 8, \"ion-card\", 17)(30, TodayPage_ion_card_30_Template, 7, 0, \"ion-card\", 18);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(31, \"ion-modal\", 19);\n      i0.ɵɵlistener(\"ionModalDidDismiss\", function TodayPage_Template_ion_modal_ionModalDidDismiss_31_listener() {\n        return ctx.closeAddQuestModal();\n      });\n      i0.ɵɵtemplate(32, TodayPage_ng_template_32_Template, 131, 12, \"ng-template\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(33, TodayPage_app_celebration_33_Template, 1, 2, \"app-celebration\", 20);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(34, \"app-navigation\");\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"fullscreen\", true);\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"headerText\", ctx.headerText);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngForOf\", ctx.weekDates);\n      i0.ɵɵadvance(13);\n      i0.ɵɵproperty(\"ngif\", ctx.quests.length === 0);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngForOf\", ctx.quests);\n      i0.ɵɵadvance(7);\n      i0.ɵɵproperty(\"ngIf\", ctx.dailyQuest && ctx.dailyQuest.current_quest);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", !ctx.dailyQuest);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"isOpen\", true)(\"backdropDismiss\", true)(\"breakpoints\", i0.ɵɵpureFunction0(12, _c0))(\"initialBreakpoint\", 0.75);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.showCelebration);\n    }\n  },\n  dependencies: [IonicModule, i1.IonButton, i1.IonButtons, i1.IonCard, i1.IonCardContent, i1.IonCardHeader, i1.IonCardTitle, i1.IonCheckbox, i1.IonCol, i1.IonContent, i1.IonFooter, i1.IonGrid, i1.IonHeader, i1.IonIcon, i1.IonInput, i1.IonRange, i1.IonRow, i1.IonSelect, i1.IonSelectOption, i1.IonText, i1.IonTextarea, i1.IonTitle, i1.IonToolbar, i1.IonModal, i1.BooleanValueAccessor, i1.NumericValueAccessor, i1.SelectValueAccessor, i1.TextValueAccessor, i1.IonMinValidator, CommonModule, i2.NgForOf, i2.NgIf, FormsModule, i3.ɵNgNoValidate, i3.NgControlStatus, i3.NgControlStatusGroup, i3.RequiredValidator, i3.NgModel, i3.NgForm, NavigationComponent, CelebrationComponent, EmojiInputDirective, HeaderComponent],\n  styles: [\"ion-content[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 50%;\\n  margin: 0 auto;\\n  position: relative;\\n}\\nion-content[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%]   .date-progress[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  border-radius: 50%;\\n  pointer-events: none;\\n  z-index: 0;\\n  transform: rotate(-90deg);\\n}\\nion-content[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%]   .date-progress[_ngcontent-%COMP%]   circle[_ngcontent-%COMP%] {\\n  fill: transparent;\\n  stroke-width: 5;\\n  stroke-linecap: round;\\n  transform-origin: center;\\n  transition: stroke-dasharray 0.5s ease;\\n}\\nion-content[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%]   .date-progress[_ngcontent-%COMP%]   .background-circle[_ngcontent-%COMP%] {\\n  stroke: rgba(255, 255, 255, 0.1);\\n  stroke-width: 5;\\n}\\nion-content[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%]   .date-progress[_ngcontent-%COMP%]   .progress-circle[_ngcontent-%COMP%] {\\n  stroke: #4169E1;\\n  stroke-opacity: 1;\\n  stroke-width: 5;\\n}\\nion-content[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%]   .date-progress[_ngcontent-%COMP%]   .progress-circle.low[_ngcontent-%COMP%] {\\n  stroke: #FF9500 !important;\\n}\\nion-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .week-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-around;\\n}\\nion-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .week-row[_ngcontent-%COMP%]   .day-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  width: 40px;\\n}\\nion-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .week-row[_ngcontent-%COMP%]   .day-container[_ngcontent-%COMP%]   .day-name[_ngcontent-%COMP%] {\\n  padding: 5px;\\n  font-size: 12px;\\n  color: white;\\n  margin-bottom: 8px;\\n  font-weight: lighter;\\n  width: 22px;\\n  height: 22px;\\n  border-radius: 50%;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\nion-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .week-row[_ngcontent-%COMP%]   .day-container[_ngcontent-%COMP%]   .day-name.active[_ngcontent-%COMP%] {\\n  background-color: var(--accent);\\n}\\nion-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .week-row[_ngcontent-%COMP%]   .day-container[_ngcontent-%COMP%]   .day-name.selected[_ngcontent-%COMP%] {\\n  background-color: var(--text-muted);\\n}\\nion-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .week-row[_ngcontent-%COMP%]   .day-container[_ngcontent-%COMP%]   .date-progress[_ngcontent-%COMP%]   .progress-circle[_ngcontent-%COMP%] {\\n  stroke: #4169E1;\\n  stroke-opacity: 0.9;\\n}\\nion-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .week-row[_ngcontent-%COMP%]   .day-container[_ngcontent-%COMP%]   .date.disabled[_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\nion-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .week-row[_ngcontent-%COMP%]   .day-container[_ngcontent-%COMP%]   .date.unselected[_ngcontent-%COMP%] {\\n  color: var(--text-muted);\\n}\\nion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%] {\\n  padding: 0;\\n}\\nion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .heartbeat-circle[_ngcontent-%COMP%] {\\n  margin: 32px;\\n  width: 100px;\\n  height: 100px;\\n  background: linear-gradient(220deg, #4169e1 0%, #6b85e8 20%, #95a5ef 40%, #bfc5f6 60%, #e7e9fd 80%, #ffffff 100%);\\n  background-size: 300% 100%;\\n  border-radius: 50%;\\n  position: relative;\\n  animation: _ngcontent-%COMP%_heartbeat 1.2s infinite, _ngcontent-%COMP%_gradient 2s ease-in-out infinite alternate;\\n}\\n@keyframes _ngcontent-%COMP%_heartbeat {\\n  0% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n  25% {\\n    transform: scale(1.03);\\n  }\\n  50% {\\n    transform: scale(1.05);\\n  }\\n  75% {\\n    transform: scale(1.03);\\n  }\\n  100% {\\n    transform: scale(1);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_gradient {\\n  0% {\\n    background-position: 100% 0%;\\n  }\\n  100% {\\n    background-position: 0% 0%;\\n  }\\n}\\nion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .big-date[_ngcontent-%COMP%] {\\n  min-width: 200px;\\n  max-width: 450px;\\n  min-height: 200px;\\n  max-height: 450px;\\n}\\nion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .big-date[_ngcontent-%COMP%]   .date-progress[_ngcontent-%COMP%]   circle[_ngcontent-%COMP%] {\\n  stroke-width: 3;\\n}\\nion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .add-quest[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\nion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .add-quest[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\nion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .add-quest[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n}\\nion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .quests[_ngcontent-%COMP%]   .no-quest-card[_ngcontent-%COMP%] {\\n  color: var(--text);\\n  margin: 16px 0;\\n  border: 1px solid var(--error);\\n}\\nion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .quests[_ngcontent-%COMP%]   .no-quest-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 20px;\\n}\\nion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .quests[_ngcontent-%COMP%]   .no-quest-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  text-align: left;\\n}\\nion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .quests[_ngcontent-%COMP%]   .no-quest-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 5rem;\\n  color: var(--error);\\n}\\nion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .quests[_ngcontent-%COMP%]   .no-quest-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n  width: 80%;\\n}\\nion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .quests[_ngcontent-%COMP%]   .quest-item[_ngcontent-%COMP%] {\\n  padding: 5px 0 5px 0;\\n  margin: 16px 0 16px 0;\\n  display: flex;\\n  flex-direction: column;\\n}\\nion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .quests[_ngcontent-%COMP%]   .quest-item[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\nion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .quests[_ngcontent-%COMP%]   .quest-item[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   .quest-info[_ngcontent-%COMP%] {\\n  align-items: flex-start;\\n}\\nion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .quests[_ngcontent-%COMP%]   .quest-item[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   .quest-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  color: var(--accent);\\n}\\nion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .quests[_ngcontent-%COMP%]   .quest-item[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   .quest-info[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  margin-top: 5px;\\n  color: var(--text-secondary);\\n}\\nion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .quests[_ngcontent-%COMP%]   .quest-item[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n}\\nion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .quests[_ngcontent-%COMP%]   .quest-item[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]   .quest-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n}\\nion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .quests[_ngcontent-%COMP%]   .quest-item[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]   .quest-streak[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: var(--text-secondary);\\n}\\nion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .quests[_ngcontent-%COMP%]   .quest-item[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%] {\\n  margin-top: 10px;\\n  width: 100%;\\n  display: flex;\\n  justify-content: center;\\n}\\nion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .quests[_ngcontent-%COMP%]   .quest-item[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]   .progress-time[_ngcontent-%COMP%], \\nion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .quests[_ngcontent-%COMP%]   .quest-item[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]   .progress[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: flex;\\n  flex-direction: column;\\n}\\nion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .quests[_ngcontent-%COMP%]   .quest-item[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%] {\\n  margin: 5px auto;\\n  color: var(--text-secondary);\\n  text-align: right;\\n}\\n\\n.add-quest-btn[_ngcontent-%COMP%] {\\n  --background: rgba(65, 105, 225, 0.1);\\n  --color: #4169E1;\\n  --border-radius: 6px;\\n  --padding-start: 12px;\\n  --padding-end: 12px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  height: 32px;\\n  margin: 0;\\n}\\n\\n.add-quest-btn[_ngcontent-%COMP%]:hover {\\n  --background: rgba(65, 105, 225, 0.2);\\n}\\n\\n.quest-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n\\n.quest-item[_ngcontent-%COMP%] {\\n  background-color: #1C1C1E;\\n  border: 1px solid #2C2C2E;\\n  border-radius: 8px;\\n  padding: 12px;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.quest-item[_ngcontent-%COMP%]:active {\\n  transform: scale(0.98);\\n}\\n\\n.quest-item.completed[_ngcontent-%COMP%] {\\n  border-color: #4169E1;\\n}\\n\\n.quest-item.completed[_ngcontent-%COMP%]   .quest-title[_ngcontent-%COMP%] {\\n  color: #4169E1;\\n}\\n\\n.quest-item.completed[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: rgba(65, 105, 225, 0.05);\\n  pointer-events: none;\\n}\\n\\n.quest-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  min-width: 24px;\\n  text-align: center;\\n}\\n\\n.quest-info[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n}\\n\\n.quest-title[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin-bottom: 2px;\\n  color: #4169E1;\\n}\\n\\n.quest-description[_ngcontent-%COMP%] {\\n  color: #8E8E93;\\n  font-size: 12px;\\n  margin-bottom: 4px;\\n}\\n\\n.progress[_ngcontent-%COMP%], \\n.progress-time[_ngcontent-%COMP%] {\\n  color: var(--secondary-text);\\n  font-size: 12px;\\n}\\n\\n\\n\\n.side-quests[_ngcontent-%COMP%] {\\n  position: relative;\\n  padding-top: 32px;\\n}\\n\\n.section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 16px;\\n}\\n\\n.quests[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], \\n.daily-side-quest[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n  margin: 0;\\n}\\n\\n.progress-slider[_ngcontent-%COMP%] {\\n  -webkit-appearance: none;\\n  -moz-appearance: none;\\n       appearance: none;\\n  width: 100%;\\n  height: 4px;\\n  background: #2C2C2E;\\n  \\n\\n  outline: none;\\n  position: relative;\\n  \\n\\n}\\n\\n.progress-slider[_ngcontent-%COMP%]::-webkit-slider-thumb {\\n  -webkit-appearance: none;\\n  appearance: none;\\n  width: 12px;\\n  height: 12px;\\n  border-radius: 50%;\\n  background: #4169E1;\\n  cursor: pointer;\\n  position: relative;\\n  margin-top: -4px;\\n  \\n\\n}\\n\\n.progress-slider[_ngcontent-%COMP%]::-moz-range-thumb {\\n  width: 12px;\\n  height: 12px;\\n  border-radius: 50%;\\n  background: #4169E1;\\n  cursor: pointer;\\n  border: none;\\n  position: relative;\\n  margin-top: 0;\\n  \\n\\n}\\n\\n.progress-slider[_ngcontent-%COMP%]::-webkit-slider-runnable-track {\\n  height: 4px;\\n  border-radius: 2px;\\n}\\n\\n.progress-slider[_ngcontent-%COMP%]::-moz-range-track {\\n  height: 4px;\\n  border-radius: 2px;\\n}\\n\\n\\n\\n.progress-slider[_ngcontent-%COMP%] {\\n  \\n\\n  background: var(--inactive-date);\\n}\\n\\n\\n\\ninput[type=range].progress-slider[_ngcontent-%COMP%] {\\n  -webkit-appearance: none;\\n  -moz-appearance: none;\\n       appearance: none;\\n  height: 4px;\\n  border-radius: 2px;\\n  outline: none;\\n  position: relative;\\n  z-index: 1;\\n  \\n\\n}\\n\\ninput[type=range].progress-slider[_ngcontent-%COMP%]::-webkit-slider-runnable-track {\\n  height: 4px;\\n  border-radius: 2px;\\n  background: transparent;\\n}\\n\\ninput[type=range].progress-slider[_ngcontent-%COMP%]::-moz-range-track {\\n  height: 4px;\\n  border-radius: 2px;\\n  background: transparent;\\n}\\n\\n\\n\\ninput[type=range].progress-slider[_ngcontent-%COMP%]::-webkit-slider-thumb {\\n  -webkit-appearance: none;\\n  appearance: none;\\n  width: 12px;\\n  height: 12px;\\n  border-radius: 50%;\\n  background: #4169E1;\\n  \\n\\n  cursor: pointer;\\n  margin-top: -4px;\\n  \\n\\n  position: relative;\\n  z-index: 2;\\n}\\n\\ninput[type=range].progress-slider[_ngcontent-%COMP%]::-moz-range-thumb {\\n  width: 12px;\\n  height: 12px;\\n  border-radius: 50%;\\n  background: #4169E1;\\n  \\n\\n  cursor: pointer;\\n  border: none;\\n  position: relative;\\n  margin-top: 0;\\n  \\n\\n  z-index: 2;\\n}\\n\\n.progress-text[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: var(--secondary-text);\\n  margin-top: 2px;\\n}\\n\\n.container[_ngcontent-%COMP%] {\\n  width: 480px;\\n  margin: 0 auto;\\n  padding: 20px;\\n  overflow-y: auto;\\n  scrollbar-width: none;\\n  height: 100%;\\n  padding-bottom: 74px;\\n}\\n\\n.container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none;\\n  \\n\\n}\\n\\n.header-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 24px;\\n}\\n\\n.logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.logo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  height: 24px;\\n}\\n\\n.logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n}\\n\\n.page-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n}\\n\\n.week-calendar[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n\\n.days[_ngcontent-%COMP%], \\n.dates[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(7, 1fr);\\n  text-align: center;\\n  gap: 8px;\\n}\\n\\n.day-name[_ngcontent-%COMP%] {\\n  color: var(--secondary-text);\\n  font-size: 14px;\\n}\\n\\n.date-content[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1;\\n}\\n\\nh2[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  margin-bottom: 16px;\\n}\\n\\n.side-quests[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 90%;\\n  height: 1px;\\n  background: linear-gradient(to right, transparent, #4B0082, transparent);\\n}\\n\\n.calendar[_ngcontent-%COMP%] {\\n  margin: 20px 0;\\n  padding: 10px;\\n  background: var(--bg-secondary);\\n  border-radius: 8px;\\n}\\n\\n.calendar-nav[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  gap: 10px;\\n}\\n\\n.calendar-days[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(7, 1fr);\\n  gap: 5px;\\n  text-align: center;\\n}\\n\\n.day-name[_ngcontent-%COMP%] {\\n  color: #8E8E93;\\n  font-size: 12px;\\n  font-weight: 500;\\n}\\n\\n.day-number[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 30px;\\n  height: 30px;\\n  border-radius: 50%;\\n  cursor: pointer;\\n  text-decoration: none;\\n  color: var(--text-primary);\\n  margin: 0 auto;\\n}\\n\\n.day-number[_ngcontent-%COMP%]:hover {\\n  background: var(--bg-hover);\\n}\\n\\n.day-number.selected[_ngcontent-%COMP%] {\\n  background: var(--primary-color);\\n  color: white;\\n}\\n\\n.day-number.today[_ngcontent-%COMP%] {\\n  border: 2px solid var(--primary-color);\\n}\\n\\n.nav-arrow[_ngcontent-%COMP%] {\\n  --background: transparent;\\n  --color: #FFFFFF;\\n  --border-radius: 50%;\\n  --padding-start: 0;\\n  --padding-end: 0;\\n  width: 32px;\\n  height: 32px;\\n  margin: 0;\\n  font-size: 18px;\\n  cursor: pointer;\\n}\\n\\n.nav-arrow[_ngcontent-%COMP%]:hover {\\n  --background: rgba(255, 255, 255, 0.1);\\n}\\n\\n.time-display[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n  color: var(--text-color);\\n  margin-right: 16px;\\n}\\n\\n\\n\\ninput[type=number][_ngcontent-%COMP%]::-webkit-inner-spin-button, \\ninput[type=number][_ngcontent-%COMP%]::-webkit-outer-spin-button {\\n  opacity: 0.3;\\n}\\n\\n\\n\\nion-range.progress-slider[_ngcontent-%COMP%] {\\n  --bar-height: 6px;\\n  --bar-border-radius: 3px;\\n  --knob-size: 16px;\\n  --bar-background: #2C2C2E;\\n  --bar-background-active: #4169E1;\\n  --knob-background: #4169E1;\\n  --pin-background: #4169E1;\\n  --pin-color: #FFFFFF;\\n  --step: 1;\\n  --tick-height: 0;\\n  --tick-width: 0;\\n  --tick-background: transparent;\\n  --tick-background-active: transparent;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n\\n\\n.progress-slider[_ngcontent-%COMP%] {\\n  -webkit-appearance: none;\\n  -moz-appearance: none;\\n       appearance: none;\\n  width: 100%;\\n  height: 6px;\\n  border-radius: 3px;\\n  outline: none;\\n  background: linear-gradient(to right, #4169E1 0%, #4169E1 var(--progress-value), #2C2C2E var(--progress-value), #2C2C2E 100%);\\n}\\n\\n\\n\\nion-range.progress-slider[_ngcontent-%COMP%]::part(tick) {\\n  display: none !important;\\n}\\n\\nion-range.progress-slider[_ngcontent-%COMP%]::part(tick-active) {\\n  display: none !important;\\n}\\n\\n.progress-slider[_ngcontent-%COMP%]::-webkit-slider-thumb {\\n  -webkit-appearance: none;\\n  appearance: none;\\n  width: 16px;\\n  height: 16px;\\n  border-radius: 50%;\\n  background: #4169E1;\\n  cursor: pointer;\\n  margin-top: -5px;\\n}\\n\\n.progress-slider[_ngcontent-%COMP%]::-moz-range-thumb {\\n  width: 16px;\\n  height: 16px;\\n  border-radius: 50%;\\n  background: #4169E1;\\n  cursor: pointer;\\n  margin-top: 0;\\n  border: none;\\n}\\n\\n.daily-side-quest[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  padding-bottom: 20px;\\n}\\n\\n\\n\\n.add-quest-modal[_ngcontent-%COMP%] {\\n  --background: transparent;\\n  --backdrop-opacity: 0.6;\\n  --width: min(480px, 95vw);\\n  --height: auto;\\n  --max-height: 90vh;\\n  --border-radius: 24px;\\n  --box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4),\\n                0 0 0 1px rgba(255, 255, 255, 0.1);\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .modal-wrapper[_ngcontent-%COMP%] {\\n  border-radius: var(--border-radius);\\n  overflow: hidden;\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  background: rgba(28, 28, 30, 0.95);\\n  border: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%] {\\n  --background: rgba(28, 28, 30, 0.98);\\n  --border-color: rgba(255, 255, 255, 0.1);\\n  border-bottom: 1px solid var(--border-color);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%] {\\n  --background: transparent;\\n  --padding-start: 24px;\\n  --padding-end: 24px;\\n  --min-height: 64px;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .modal-title[_ngcontent-%COMP%]   .title-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .modal-title[_ngcontent-%COMP%]   .title-content[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.3));\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .modal-title[_ngcontent-%COMP%]   .title-content[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 700;\\n  color: var(--text);\\n  background: linear-gradient(135deg, #fff, #e0e0e0);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%] {\\n  --color: var(--text-secondary);\\n  --background: rgba(255, 255, 255, 0.05);\\n  --border-radius: 12px;\\n  --padding-start: 12px;\\n  --padding-end: 12px;\\n  height: 40px;\\n  width: 40px;\\n  transition: all 0.3s ease;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]:hover {\\n  --background: rgba(255, 255, 255, 0.1);\\n  --color: var(--text);\\n  transform: scale(1.05);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%] {\\n  --background: transparent;\\n  --padding-start: 0;\\n  --padding-end: 0;\\n  --padding-top: 0;\\n  --padding-bottom: 0;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%] {\\n  padding: 24px;\\n  max-height: calc(90vh - 140px);\\n  overflow-y: auto;\\n  scrollbar-width: thin;\\n  scrollbar-color: rgba(255, 255, 255, 0.2) transparent;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: transparent;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(255, 255, 255, 0.2);\\n  border-radius: 3px;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: var(--text);\\n  margin: 0 0 8px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .section-divider[_ngcontent-%COMP%] {\\n  height: 2px;\\n  background: linear-gradient(90deg, var(--accent) 0%, rgba(65, 105, 225, 0.3) 50%, transparent 100%);\\n  border-radius: 1px;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .quest-identity-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  margin-bottom: 20px;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .quest-identity-row[_ngcontent-%COMP%]   .emoji-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  flex-shrink: 0;\\n  width: 80px;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .quest-identity-row[_ngcontent-%COMP%]   .emoji-container[_ngcontent-%COMP%]   .emoji-input[_ngcontent-%COMP%] {\\n  --background: rgba(255, 255, 255, 0.05);\\n  --color: var(--text);\\n  --border-color: rgba(255, 255, 255, 0.1);\\n  --border-radius: 16px;\\n  --padding-start: 0;\\n  --padding-end: 0;\\n  height: 56px;\\n  text-align: center;\\n  font-size: 24px;\\n  border: 2px solid var(--border-color);\\n  transition: all 0.3s ease;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .quest-identity-row[_ngcontent-%COMP%]   .emoji-container[_ngcontent-%COMP%]   .emoji-input[_ngcontent-%COMP%]:focus-within {\\n  --border-color: var(--accent);\\n  box-shadow: 0 0 0 4px rgba(65, 105, 225, 0.1);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .quest-identity-row[_ngcontent-%COMP%]   .emoji-container[_ngcontent-%COMP%]   .emoji-label[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: -20px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  font-size: 12px;\\n  color: var(--text-secondary);\\n  font-weight: 500;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .quest-identity-row[_ngcontent-%COMP%]   .name-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  position: relative;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin-bottom: 24px;\\n  \\n\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .floating-label[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 16px;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  color: var(--text-secondary);\\n  font-size: 16px;\\n  font-weight: 500;\\n  pointer-events: none;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  z-index: 1;\\n  background: transparent;\\n  padding: 0 4px;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]:focus-within    + .floating-label[_ngcontent-%COMP%], \\n.add-quest-modal[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]:not([value=\\\"\\\"])    + .floating-label[_ngcontent-%COMP%], \\n.add-quest-modal[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   ion-input.has-value[_ngcontent-%COMP%]    + .floating-label[_ngcontent-%COMP%], \\n.add-quest-modal[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   ion-textarea[_ngcontent-%COMP%]:focus-within    + .floating-label[_ngcontent-%COMP%], \\n.add-quest-modal[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   ion-textarea[_ngcontent-%COMP%]:not([value=\\\"\\\"])    + .floating-label[_ngcontent-%COMP%], \\n.add-quest-modal[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]:focus-within    + .floating-label[_ngcontent-%COMP%], \\n.add-quest-modal[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   ion-select.has-value[_ngcontent-%COMP%]    + .floating-label[_ngcontent-%COMP%] {\\n  top: 0;\\n  transform: translateY(-50%);\\n  font-size: 12px;\\n  color: var(--accent);\\n  background: rgba(28, 28, 30, 0.9);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .name-input[_ngcontent-%COMP%], \\n.add-quest-modal[_ngcontent-%COMP%]   .description-input[_ngcontent-%COMP%], \\n.add-quest-modal[_ngcontent-%COMP%]   .goal-input[_ngcontent-%COMP%] {\\n  --background: rgba(255, 255, 255, 0.05);\\n  --color: var(--text);\\n  --border-color: rgba(255, 255, 255, 0.1);\\n  --border-radius: 16px;\\n  --padding-start: 16px;\\n  --padding-end: 16px;\\n  --padding-top: 20px;\\n  --padding-bottom: 12px;\\n  height: 56px;\\n  border: 2px solid var(--border-color);\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .name-input[_ngcontent-%COMP%]:focus-within, \\n.add-quest-modal[_ngcontent-%COMP%]   .description-input[_ngcontent-%COMP%]:focus-within, \\n.add-quest-modal[_ngcontent-%COMP%]   .goal-input[_ngcontent-%COMP%]:focus-within {\\n  --border-color: var(--accent);\\n  box-shadow: 0 0 0 4px rgba(65, 105, 225, 0.1);\\n  --background: rgba(255, 255, 255, 0.08);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .description-input[_ngcontent-%COMP%] {\\n  height: auto;\\n  min-height: 80px;\\n  --padding-top: 24px;\\n  resize: vertical;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .modern-select[_ngcontent-%COMP%] {\\n  --background: rgba(255, 255, 255, 0.05);\\n  --color: var(--text);\\n  --border-color: rgba(255, 255, 255, 0.1);\\n  --border-radius: 16px;\\n  --padding-start: 16px;\\n  --padding-end: 16px;\\n  --padding-top: 20px;\\n  --padding-bottom: 12px;\\n  height: 56px;\\n  border: 2px solid var(--border-color);\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .modern-select[_ngcontent-%COMP%]:focus-within {\\n  --border-color: var(--accent);\\n  box-shadow: 0 0 0 4px rgba(65, 105, 225, 0.1);\\n  --background: rgba(255, 255, 255, 0.08);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .modern-select.unit-select[_ngcontent-%COMP%] {\\n  min-width: 120px;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .goal-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  margin-bottom: 20px;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .goal-row[_ngcontent-%COMP%]   .goal-value-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  position: relative;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .goal-row[_ngcontent-%COMP%]   .goal-unit-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  position: relative;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .priority-warning[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-top: 8px;\\n  padding: 12px 16px;\\n  background: rgba(255, 159, 0, 0.1);\\n  border: 1px solid rgba(255, 159, 0, 0.3);\\n  border-radius: 12px;\\n  font-size: 14px;\\n  color: #FF9F00;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .priority-warning[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .schedule-section[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  padding: 20px;\\n  background: rgba(255, 255, 255, 0.03);\\n  border: 1px solid rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .schedule-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: var(--text);\\n  margin: 0 0 16px 0;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .schedule-section[_ngcontent-%COMP%]   .days-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));\\n  gap: 12px;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .schedule-section[_ngcontent-%COMP%]   .month-days-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));\\n  gap: 8px;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .schedule-section[_ngcontent-%COMP%]   .day-item[_ngcontent-%COMP%], \\n.add-quest-modal[_ngcontent-%COMP%]   .schedule-section[_ngcontent-%COMP%]   .month-day-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 12px 8px;\\n  background: rgba(255, 255, 255, 0.05);\\n  border: 1px solid rgba(255, 255, 255, 0.1);\\n  border-radius: 12px;\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .schedule-section[_ngcontent-%COMP%]   .day-item[_ngcontent-%COMP%]:hover, \\n.add-quest-modal[_ngcontent-%COMP%]   .schedule-section[_ngcontent-%COMP%]   .month-day-item[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.08);\\n  border-color: rgba(255, 255, 255, 0.2);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .schedule-section[_ngcontent-%COMP%]   .day-item[_ngcontent-%COMP%]   .modern-checkbox[_ngcontent-%COMP%], \\n.add-quest-modal[_ngcontent-%COMP%]   .schedule-section[_ngcontent-%COMP%]   .month-day-item[_ngcontent-%COMP%]   .modern-checkbox[_ngcontent-%COMP%] {\\n  --background: transparent;\\n  --background-checked: var(--accent);\\n  --border-color: rgba(255, 255, 255, 0.3);\\n  --border-color-checked: var(--accent);\\n  --checkmark-color: white;\\n  --size: 20px;\\n  --border-radius: 6px;\\n  margin: 0;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .schedule-section[_ngcontent-%COMP%]   .day-item[_ngcontent-%COMP%]   .day-label[_ngcontent-%COMP%], \\n.add-quest-modal[_ngcontent-%COMP%]   .schedule-section[_ngcontent-%COMP%]   .day-item[_ngcontent-%COMP%]   .month-day-label[_ngcontent-%COMP%], \\n.add-quest-modal[_ngcontent-%COMP%]   .schedule-section[_ngcontent-%COMP%]   .month-day-item[_ngcontent-%COMP%]   .day-label[_ngcontent-%COMP%], \\n.add-quest-modal[_ngcontent-%COMP%]   .schedule-section[_ngcontent-%COMP%]   .month-day-item[_ngcontent-%COMP%]   .month-day-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: var(--text);\\n  text-align: center;\\n  margin: 0;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .modal-footer[_ngcontent-%COMP%] {\\n  --background: rgba(28, 28, 30, 0.98);\\n  --border-color: rgba(255, 255, 255, 0.1);\\n  border-top: 1px solid var(--border-color);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .modal-footer[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%] {\\n  --background: transparent;\\n  --padding-start: 24px;\\n  --padding-end: 24px;\\n  --min-height: 80px;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .modal-footer[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .footer-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  width: 100%;\\n  gap: 16px;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .modal-footer[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .footer-buttons[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%] {\\n  --color: var(--text-secondary);\\n  --background: transparent;\\n  --border-radius: 12px;\\n  --padding-start: 20px;\\n  --padding-end: 20px;\\n  height: 48px;\\n  font-weight: 600;\\n  transition: all 0.3s ease;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .modal-footer[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .footer-buttons[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]:hover {\\n  --color: var(--text);\\n  --background: rgba(255, 255, 255, 0.05);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .modal-footer[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .footer-buttons[_ngcontent-%COMP%]   .create-quest-btn[_ngcontent-%COMP%] {\\n  --background: linear-gradient(135deg, var(--accent), #5a7fff);\\n  --color: white;\\n  --border-radius: 16px;\\n  --padding-start: 24px;\\n  --padding-end: 24px;\\n  height: 48px;\\n  font-weight: 700;\\n  font-size: 16px;\\n  box-shadow: 0 8px 24px rgba(65, 105, 225, 0.3);\\n  transition: all 0.3s ease;\\n  flex: 1;\\n  max-width: 200px;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .modal-footer[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .footer-buttons[_ngcontent-%COMP%]   .create-quest-btn[_ngcontent-%COMP%]:hover:not([disabled]) {\\n  transform: translateY(-2px);\\n  box-shadow: 0 12px 32px rgba(65, 105, 225, 0.4);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .modal-footer[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .footer-buttons[_ngcontent-%COMP%]   .create-quest-btn[_ngcontent-%COMP%]:active:not([disabled]) {\\n  transform: translateY(0);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .modal-footer[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .footer-buttons[_ngcontent-%COMP%]   .create-quest-btn[disabled][_ngcontent-%COMP%] {\\n  --background: rgba(255, 255, 255, 0.1);\\n  --color: var(--text-secondary);\\n  box-shadow: none;\\n  opacity: 0.6;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .modal-footer[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .footer-buttons[_ngcontent-%COMP%]   .create-quest-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n});", "map": {"version": 3, "names": ["inject", "CommonModule", "FormsModule", "IonicModule", "QuestService", "SideQuestService", "UserService", "SupabaseService", "Quest", "Subscription", "fork<PERSON><PERSON>n", "map", "of", "switchMap", "take", "firstValueFrom", "NavigationComponent", "CelebrationComponent", "ActivatedRoute", "Router", "PreferencesService", "EmojiInputDirective", "StreakCalculatorService", "HeaderComponent", "i0", "ɵɵelement", "ɵɵclassProp", "date_r2", "completion_percentage", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "TodayPage_div_8_Template_div_click_3_listener", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "is_future", "selectDate", "ɵɵtemplate", "TodayPage_div_8__svg_circle_6_Template", "ɵɵadvance", "is_today", "is_selected", "ɵɵtextInterpolate1", "ɵɵpureFunction0", "_c1", "i_r4", "ɵɵproperty", "ɵɵtwoWayListener", "TodayPage_ion_card_22_div_11_Template_ion_range_ngModelChange_1_listener", "$event", "_r7", "quest_r6", "ɵɵtwoWayBindingSet", "value_achieved", "TodayPage_ion_card_22_div_11_Template_ion_range_ionChange_1_listener", "updateQuestProgress", "TodayPage_ion_card_22_div_11_Template_ion_range_ionInput_1_listener", "target", "updateSliderBackground", "ɵɵstyleMapInterpolate1", "goal_value", "ɵɵtwoWayProperty", "ɵɵtextInterpolate4", "goal_unit", "ɵɵelementContainerStart", "TodayPage_ion_card_22_div_12_Template_ion_range_ngModelChange_1_listener", "_r8", "TodayPage_ion_card_22_div_12_Template_ion_range_ionChange_1_listener", "TodayPage_ion_card_22_div_12_Template_ion_range_ionInput_1_listener", "TodayPage_ion_card_22_div_12_ng_container_4_Template", "ɵɵtextInterpolate2", "streak", "TodayPage_ion_card_22_Template_ion_card_click_0_listener", "_r5", "toggleQuest", "TodayPage_ion_card_22_div_11_Template", "TodayPage_ion_card_22_div_12_Template", "TodayPage_ion_card_22_ion_text_14_Template", "completed", "emoji", "ɵɵtextInterpolate", "name", "description", "isSameDay", "selectedDate", "get<PERSON><PERSON>y", "dailyQuest", "TodayPage_ion_card_29_Template_ion_card_click_0_listener", "_r9", "toggleSideQuest", "TodayPage_ion_card_29_div_11_Template", "current_quest", "TodayPage_ng_template_32_div_60_Template_ion_select_ngModelChange_1_listener", "_r11", "newQuest", "priority", "TodayPage_ng_template_32_div_60_div_8_Template", "hasHighPriorityQuest", "TodayPage_ng_template_32_div_121_div_4_Template_ion_checkbox_ionChange_1_listener", "day_r13", "_r12", "updateDaysOfWeek", "value", "toLowerCase", "label", "TodayPage_ng_template_32_div_121_div_4_Template", "weekDays", "TodayPage_ng_template_32_div_122_div_4_Template_ion_checkbox_ionChange_1_listener", "day_r15", "_r14", "updateDaysOfMonth", "TodayPage_ng_template_32_div_122_div_4_Template", "monthDays", "TodayPage_ng_template_32_Template_ion_button_click_9_listener", "_r10", "closeAddQuestModal", "TodayPage_ng_template_32_Template_form_ngSubmit_13_listener", "createQuest", "TodayPage_ng_template_32_Template_ion_input_ngModelChange_22_listener", "TodayPage_ng_template_32_Template_ion_input_ngModelChange_26_listener", "TodayPage_ng_template_32_Template_ion_textarea_ngModelChange_30_listener", "TodayPage_ng_template_32_Template_ion_select_ngModelChange_39_listener", "quest_type", "TodayPage_ng_template_32_Template_ion_select_ngModelChange_47_listener", "category", "TodayPage_ng_template_32_Template_ion_select_ionChange_47_listener", "checkCategoryPriority", "TodayPage_ng_template_32_div_60_Template", "TodayPage_ng_template_32_Template_ion_input_ngModelChange_68_listener", "TodayPage_ng_template_32_Template_ion_select_ngModelChange_72_listener", "TodayPage_ng_template_32_Template_ion_select_ngModelChange_112_listener", "goal_period", "TodayPage_ng_template_32_Template_ion_select_ionChange_112_listener", "updatePeriodDisplay", "TodayPage_ng_template_32_div_121_Template", "TodayPage_ng_template_32_div_122_Template", "TodayPage_ng_template_32_Template_ion_button_click_126_listener", "TodayPage_ng_template_32_Template_ion_button_click_128_listener", "questForm_r16", "valid", "TodayPage_app_celebration_33_Template_app_celebration_close_0_listener", "_r17", "closeCelebration", "currentUser", "formatDate", "TodayPage", "loadDailySideQuest", "today", "Date", "setHours", "isTodaySelected", "getTime", "showSidequests", "userId", "sideQuestService", "ensureUserHasDailySideQuests", "pipe", "subscribe", "next", "sideQuests", "length", "sideQuest", "supabaseService", "getClient", "from", "select", "eq", "current_quest_id", "single", "then", "response", "error", "questDetails", "data", "id", "constructor", "user$", "weekDates", "dayNames", "headerText", "weekOffset", "quests", "questCache", "isLoadingData", "showAddQuestModal", "getEmptyQuest", "showCelebration", "celebrationShownDates", "Array", "_", "i", "selectedDaysOfWeek", "selectedDaysOfMonth", "questService", "userService", "route", "router", "preferencesService", "streakCalculator", "isRedirecting", "weekProgressCache", "isChangingWeek", "togglingQuestIds", "updatingQuestIds", "togglingSideQuestIds", "queryParams", "params", "dateParam", "weekOffsetParam", "console", "log", "undefined", "parseInt", "test", "generateWeekDates", "updateHeaderText", "loadData", "userSubscription", "currentUser$", "authUser", "getUserById", "userData", "userDataSubscription", "user", "sidequests_switch", "add", "ngOnInit", "setTimeout", "preloadWeekData", "todayStr", "allKeys", "localStorage", "key", "push", "for<PERSON>ach", "startsWith", "removeItem", "todayCelebrationShown", "getItem", "ionViewWillEnter", "_this", "_asyncToGenerator", "_currentUser", "ensureUserExists", "navigateByUrl", "endDate", "end_of_current_plan", "currentDate", "isValidPlan", "<PERSON><PERSON><PERSON>", "requestAnimationFrame", "initializeSliderBackgrounds", "url", "navigate", "date", "week_offset", "replaceUrl", "sliders", "document", "querySelectorAll", "slider", "HTMLInputElement", "sliderQuestId", "getAttribute", "slider<PERSON><PERSON><PERSON>", "minValue", "min", "maxValue", "max", "percentage", "style", "background", "setAttribute", "HTMLElement", "tagName", "valueAttr", "minAttr", "maxAttr", "setProperty", "toString", "ionViewWillLeave", "ngOnDestroy", "unsubscribe", "_this2", "todayDateString", "lastStreakCalculation", "get", "getQuests", "_ref", "quest", "checkMissedDays", "createQuitQuestProgressForToday", "_x", "apply", "arguments", "recalculateSideQuestStreak", "filteredQuests", "filterQuestsForDate", "sortedFilteredQuests", "sort", "a", "b", "created_at", "localeCompare", "getQuestProgressForDate", "allProgress", "progressLookup", "progress", "quest_id", "calculateStreaks", "streaks", "calculatedStreak", "updateQuestStreak", "result", "set", "catch", "Promise", "resolve", "questsWithProgress", "sortedQuests", "checkAllQuestsCompleted", "updateWeekDateProgress", "currentDay", "getDay", "daysFromMonday", "startOfWeek", "setDate", "getDate", "dateString", "isToday", "isSelected", "isFuture", "totalQuests", "completedQuests", "completionPercentage", "cached", "total", "Math", "round", "day", "total_quests", "completed_quests", "weekDate", "index", "cachedQuests", "filter", "q", "date<PERSON><PERSON>j", "dayOfWeek", "djangoDayOfWeek", "dayOfMonth", "task_days_of_week", "task_days_of_month", "active", "createdDate", "taskDays", "split", "trim", "isArray", "dayNameShort", "getDayNameShort", "dayNameFull", "getDayNameFull", "isIncluded", "includes", "dateData", "selectedDateData", "formattedDate", "changeWeek", "direction", "allQuests", "dateObservables", "activeQuests", "emptyProgress", "progressList", "questIds", "relevantProgress", "p", "results", "findIndex", "wd", "toLocaleDateString", "weekday", "month", "_this3", "event", "_this4", "wasCompletedBefore", "detail", "questCopy", "toggleQuestCompletion", "isCompletedNow", "cachedQuestIndex", "getQuest", "calculateStreak", "todayString", "todayIndex", "updateProgressRingForDate", "updateQuestUI", "questElement", "querySelector", "classList", "remove", "streakElements", "streakValue", "element", "parentElement", "contains", "display", "textContent", "progressText", "_progressText$parentE", "isTimeUnit", "unitSuffix", "goalUnitSuffix", "sliderElement", "_this5", "newValue", "newCompletedState", "updateSideQuestUI", "toggleSideQuestCompletion", "goalUnit", "offsetHeight", "openAddQuestModal", "preventDefault", "_this6", "join", "userProfile", "userError", "Error", "questToCreate", "user_id", "questId", "questError", "message", "alert", "isChecked", "checked", "indexOf", "splice", "some", "hasHighPriority", "celebrationShown", "allQuestsCompleted", "every", "sideQuestCompleted", "show_celebration", "setItem", "year", "getFullYear", "String", "getMonth", "padStart", "date1", "date2", "djangoDayIndex", "dayMap", "selectors", "decls", "vars", "consts", "template", "TodayPage_Template", "rf", "ctx", "TodayPage_div_8_Template", "TodayPage_Template_ion_button_click_17_listener", "TodayPage_ion_card_21_Template", "TodayPage_ion_card_22_Template", "TodayPage_ion_card_29_Template", "TodayPage_ion_card_30_Template", "TodayPage_Template_ion_modal_ionModalDidDismiss_31_listener", "TodayPage_ng_template_32_Template", "TodayPage_app_celebration_33_Template", "_c0", "i1", "IonButton", "IonButtons", "IonCard", "IonCardContent", "IonCardHeader", "IonCardTitle", "IonCheckbox", "IonCol", "IonContent", "<PERSON><PERSON><PERSON><PERSON>", "IonGrid", "IonHeader", "IonIcon", "IonInput", "IonRange", "IonRow", "IonSelect", "IonSelectOption", "IonText", "IonTextarea", "IonTitle", "IonToolbar", "IonModal", "BooleanValueAccessor", "NumericValueAccessor", "SelectValueAccessor", "TextValueAccessor", "IonMinValidator", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i3", "ɵNgNoValidate", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "NgModel", "NgForm", "styles"], "sources": ["C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\pages\\today\\today.page.ts", "C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\pages\\today\\today.page.html"], "sourcesContent": ["import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, inject } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { IonicModule } from '@ionic/angular';\r\nimport { QuestService } from '../../services/quest.service';\r\nimport { SideQuestService } from '../../services/sidequest.service';\r\nimport { UserService } from '../../services/user.service';\r\nimport { SupabaseService } from '../../services/supabase.service';\r\nimport { Quest, QuestCategory, QuestGoalUnit, QuestPeriod, QuestPriority, QuestProgress, QuestType } from '../../models/quest.model';\r\nimport { User } from '../../models/user.model';\r\nimport { Observable, Subscription, forkJoin, map, of, switchMap, take, firstValueFrom } from 'rxjs';\r\nimport { NavigationComponent } from '../../components/navigation/navigation.component';\r\nimport { CelebrationComponent } from '../../components/celebration/celebration.component';\r\nimport { Activated<PERSON>out<PERSON>, Router } from '@angular/router';\r\nimport { PreferencesService } from '../../services/preferences.service';\r\nimport { EmojiInputDirective } from '../../directives/emoji-input.directive';\r\nimport { StreakCalculatorService } from '../../services/streak-calculator';\r\nimport { HeaderComponent } from 'src/app/components/header/header.component';\r\n\r\n\r\ninterface WeekDate {\r\n  date: string; // YYYY-MM-DD format\r\n  day: number;\r\n  is_today: boolean;\r\n  is_selected: boolean;\r\n  is_future: boolean;\r\n  total_quests: number;\r\n  completed_quests: number;\r\n  completion_percentage: number;\r\n}\r\n\r\ninterface DailyQuest {\r\n  id: string;\r\n  current_quest: {\r\n    id: string;\r\n    name: string;\r\n    description: string;\r\n    goal_value: number;\r\n    goal_unit: string;\r\n  };\r\n  streak: number;\r\n  completed: boolean;\r\n  value_achieved: number;\r\n  emoji: string;\r\n}\r\n\r\ninterface QuestDisplay extends Quest {\r\n  completed: boolean;\r\n  value_achieved: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-today',\r\n  templateUrl: './today.page.html',\r\n  styleUrls: ['./today.page.scss'],\r\n  standalone: true,\r\n  imports: [IonicModule, CommonModule, FormsModule, NavigationComponent, CelebrationComponent, EmojiInputDirective, HeaderComponent]\r\n})\r\nexport class TodayPage implements OnInit, OnDestroy {\r\n  // User data\r\n  user$: Observable<User | null> = of(null);\r\n  userId: string | null = null;\r\n  userSubscription: Subscription | undefined;\r\n  showSidequests = true;\r\n\r\n  // Date and calendar\r\n  selectedDate: Date = new Date();\r\n  weekDates: WeekDate[] = [];\r\n  dayNames: string[] = ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'];\r\n  headerText: string = 'Today';\r\n  weekOffset: number = 0;\r\n  selectedDateData: any;\r\n\r\n  // Quests\r\n  quests: QuestDisplay[] = [];\r\n  dailyQuest: DailyQuest | null = null;\r\n\r\n  // Cache for quest data to improve performance\r\n  private questCache: { [dateKey: string]: QuestDisplay[] } = {};\r\n\r\n  // Flag to track if we're currently loading data\r\n  private isLoadingData = false;\r\n\r\n  // Method to load daily side quest\r\n  private loadDailySideQuest() {\r\n    // Only load for today's date\r\n    const today = new Date();\r\n    today.setHours(0, 0, 0, 0);\r\n    const selectedDate = new Date(this.selectedDate);\r\n    selectedDate.setHours(0, 0, 0, 0);\r\n    const isTodaySelected = selectedDate.getTime() === today.getTime();\r\n\r\n    // Reset dailyQuest if not today's date\r\n    if (!isTodaySelected) {\r\n      this.dailyQuest = null;\r\n      return;\r\n    }\r\n\r\n    if (this.showSidequests && isTodaySelected && this.userId) {\r\n      // Use the ensureUserHasDailySideQuests method\r\n      this.sideQuestService.ensureUserHasDailySideQuests(this.userId!).pipe(\r\n        take(1)\r\n      ).subscribe({\r\n        next: (sideQuests) => {\r\n          if (sideQuests && sideQuests.length > 0) {\r\n            const sideQuest = sideQuests[0];\r\n\r\n            // Get the quest details from the pool\r\n            this.supabaseService.getClient()\r\n              .from('daily_sidequest_pool')\r\n              .select('*')\r\n              .eq('id', sideQuest.current_quest_id)\r\n              .single()\r\n              .then(response => {\r\n                if (response.error) {\r\n                  return;\r\n                }\r\n\r\n                const questDetails = response.data;\r\n\r\n                // Create the daily quest object\r\n                this.dailyQuest = {\r\n                  id: sideQuest.id!,\r\n                  current_quest: {\r\n                    id: sideQuest.current_quest_id!,\r\n                    name: questDetails.name || 'Daily Side Quest',\r\n                    description: questDetails.description || 'Complete this daily side quest',\r\n                    goal_value: questDetails.goal_value || 1,\r\n                    goal_unit: questDetails.goal_unit || 'count'\r\n                  },\r\n                  streak: sideQuest.streak || 0,\r\n                  completed: sideQuest.completed || false,\r\n                  value_achieved: sideQuest.value_achieved || 0,\r\n                  emoji: questDetails.emoji || '🎯'\r\n                };\r\n              });\r\n          } else {\r\n            this.dailyQuest = null;\r\n          }\r\n        },\r\n        error: () => {\r\n          this.dailyQuest = null;\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  // Add Quest Modal\r\n  showAddQuestModal = false;\r\n  newQuest = this.getEmptyQuest();\r\n  hasHighPriorityQuest = false;\r\n\r\n  // Celebration Modal\r\n  showCelebration = false;\r\n  currentUser: User | null = null;\r\n  celebrationShownDates: string[] = [];\r\n\r\n  // Days selection for new quest\r\n  weekDays = [\r\n    { value: 'Sun', label: 'Su' },\r\n    { value: 'Mon', label: 'Mo' },\r\n    { value: 'Tue', label: 'Tu' },\r\n    { value: 'Wed', label: 'We' },\r\n    { value: 'Thu', label: 'Th' },\r\n    { value: 'Fri', label: 'Fr' },\r\n    { value: 'Sat', label: 'Sa' }\r\n  ];\r\n  monthDays = Array.from({ length: 31 }, (_, i) => i + 1);\r\n  selectedDaysOfWeek: string[] = [];\r\n  selectedDaysOfMonth: number[] = [];\r\n\r\n  // Use inject instead of constructor injection\r\n  private questService = inject(QuestService);\r\n  private sideQuestService = inject(SideQuestService);\r\n  private userService = inject(UserService);\r\n  private supabaseService = inject(SupabaseService);\r\n  private route = inject(ActivatedRoute);\r\n  private router = inject(Router);\r\n  private preferencesService = inject(PreferencesService);\r\n  private streakCalculator = inject(StreakCalculatorService);\r\n  private isRedirecting = false; // Flag to prevent multiple redirects\r\n\r\n  constructor() {\r\n    // Subscribe to query params to get date and week_offset from URL\r\n    this.route.queryParams.subscribe(params => {\r\n      const dateParam = params['date'];\r\n      const weekOffsetParam = params['week_offset'];\r\n\r\n      console.log('TodayPage: Date param from URL query:', dateParam);\r\n      console.log('TodayPage: Week offset param from URL query:', weekOffsetParam);\r\n\r\n      // Process week offset parameter\r\n      if (weekOffsetParam !== undefined) {\r\n        try {\r\n          this.weekOffset = parseInt(weekOffsetParam);\r\n          console.log('TodayPage: Week offset set to:', this.weekOffset);\r\n        } catch (error) {\r\n          console.error('TodayPage: Error parsing week offset:', error);\r\n          this.weekOffset = 0;\r\n        }\r\n      } else {\r\n        this.weekOffset = 0;\r\n      }\r\n\r\n      // Process date parameter\r\n      if (dateParam) {\r\n        try {\r\n          // Validate date format (YYYY-MM-DD)\r\n          if (/^\\d{4}-\\d{2}-\\d{2}$/.test(dateParam)) {\r\n            this.selectedDate = new Date(dateParam);\r\n            console.log('TodayPage: Selected date from URL query:', this.selectedDate);\r\n          } else {\r\n            console.error('TodayPage: Invalid date format in URL query:', dateParam);\r\n            this.selectedDate = new Date(); // Default to today\r\n          }\r\n        } catch (error) {\r\n          console.error('TodayPage: Error parsing date from URL query:', error);\r\n          this.selectedDate = new Date(); // Default to today\r\n        }\r\n      } else {\r\n        this.selectedDate = new Date(); // Default to today\r\n      }\r\n\r\n      // Initialize week dates based on selected date and week offset\r\n      this.generateWeekDates();\r\n\r\n      // Update header text and load data\r\n      this.updateHeaderText();\r\n\r\n      // Only load data if we have a userId\r\n      if (this.userId) {\r\n        this.loadData();\r\n      }\r\n    });\r\n\r\n    // Subscribe to auth state changes\r\n    this.userSubscription = this.supabaseService.currentUser$.subscribe(authUser => {\r\n\r\n\r\n      if (!authUser) {\r\n        console.log('TodayPage: No authenticated user, but not redirecting');\r\n        // Removed redirect to allow direct access\r\n        return;\r\n      }\r\n\r\n      // User is authenticated, get user data\r\n      this.userId = authUser.id;\r\n\r\n\r\n      // Get user data from Supabase\r\n      this.userService.getUserById(authUser.id).subscribe(userData => {\r\n        if (!userData) {\r\n          console.log('TodayPage: No user data found, but not redirecting');\r\n          // Removed redirect to allow direct access\r\n          return;\r\n        }\r\n\r\n        console.log('TodayPage: User data loaded:', userData);\r\n        this.loadData();\r\n      });\r\n    });\r\n\r\n    // Set up user$ observable for template binding\r\n    this.user$ = this.supabaseService.currentUser$.pipe(\r\n      switchMap(authUser => {\r\n        if (!authUser) {\r\n          return of(null);\r\n        }\r\n\r\n        return this.userService.getUserById(authUser.id);\r\n      })\r\n    );\r\n\r\n    // Subscribe to user$ to get user preferences\r\n    const userDataSubscription = this.user$.subscribe({\r\n      next: (user) => {\r\n        if (user) {\r\n          this.showSidequests = user.sidequests_switch;\r\n          this.currentUser = user;\r\n        }\r\n      }\r\n    });\r\n\r\n    // Add the subscription to be cleaned up\r\n    this.userSubscription = new Subscription();\r\n    this.userSubscription.add(userDataSubscription);\r\n  }\r\n\r\n  ngOnInit() {\r\n    // Generate week dates and preload data for all days\r\n    this.generateWeekDates();\r\n\r\n    // Preload data for all days in the week\r\n    setTimeout(() => {\r\n      this.preloadWeekData();\r\n    }, 0);\r\n\r\n    // Load celebration shown dates from localStorage and clean up old ones\r\n    try {\r\n      // Get today's date\r\n      const today = new Date();\r\n      const todayStr = this.formatDate(today);\r\n\r\n      // First, collect all localStorage keys\r\n      const allKeys: string[] = [];\r\n      for (let i = 0; i < localStorage.length; i++) {\r\n        const key = localStorage.key(i);\r\n        if (key) {\r\n          allKeys.push(key);\r\n        }\r\n      }\r\n\r\n      // Find and remove all celebration_shown keys except today's\r\n      allKeys.forEach(key => {\r\n        if (key.startsWith('celebration_shown_') && key !== `celebration_shown_${todayStr}`) {\r\n          localStorage.removeItem(key);\r\n        }\r\n      });\r\n\r\n      // Check if we have a celebration shown for today\r\n      const todayCelebrationShown = localStorage.getItem(`celebration_shown_${todayStr}`);\r\n\r\n      // Add to our tracking array if found\r\n      this.celebrationShownDates = [];\r\n      if (todayCelebrationShown) {\r\n        this.celebrationShownDates.push(todayStr);\r\n      }\r\n    } catch (error) {\r\n      this.celebrationShownDates = [];\r\n    }\r\n  }\r\n\r\n  async ionViewWillEnter() {\r\n    // Authentication is now handled by the AuthGuard\r\n    // Just get the current user and load data\r\n    const authUser = this.supabaseService._currentUser.value;\r\n\r\n    if (!authUser) {\r\n      console.log('TodayPage: No authenticated user, but not redirecting');\r\n      return;\r\n    }\r\n\r\n    // Use the UserService to get or create the user document\r\n    this.userService.ensureUserExists(authUser).subscribe(userData => {\r\n      if (!userData) {\r\n        this.router.navigateByUrl('/signup');\r\n        return;\r\n      }\r\n\r\n      // Get end date\r\n      let endDate = userData.end_of_current_plan ? new Date(userData.end_of_current_plan) : null;\r\n      const currentDate = new Date();\r\n\r\n      // Compare dates properly\r\n      let isValidPlan = false;\r\n      if (endDate instanceof Date) {\r\n        isValidPlan = endDate > currentDate;\r\n      }\r\n\r\n      if (!isValidPlan) {\r\n        // Prevent multiple redirects\r\n        if (this.isRedirecting) return;\r\n        this.isRedirecting = true;\r\n\r\n        setTimeout(() => {\r\n          this.router.navigateByUrl('/pricing');\r\n          setTimeout(() => {\r\n            this.isRedirecting = false;\r\n          }, 2000);\r\n        }, 500);\r\n        return;\r\n      }\r\n\r\n      // Check if we have cached data for this date\r\n      const dateKey = this.formatDate(this.selectedDate);\r\n      if (this.questCache[dateKey]) {\r\n        // Use cached data\r\n        this.quests = this.questCache[dateKey];\r\n\r\n        // Initialize slider backgrounds immediately\r\n        requestAnimationFrame(() => {\r\n          this.initializeSliderBackgrounds();\r\n        });\r\n\r\n        // Load daily side quest if needed\r\n        this.loadDailySideQuest();\r\n      } else {\r\n        // Load data with the current selected date\r\n        this.loadData();\r\n      }\r\n    });\r\n\r\n    // Make sure the URL reflects the selected date and week offset\r\n    const route = this.router.url;\r\n    const dateParam = this.formatDate(this.selectedDate);\r\n\r\n    if (route === '/today') {\r\n      // If we're on the base route, update to include the date and week_offset as query parameters\r\n      this.router.navigate(['/today'], {\r\n        queryParams: {\r\n          date: dateParam,\r\n          week_offset: this.weekOffset !== 0 ? this.weekOffset : null\r\n        },\r\n        replaceUrl: true\r\n      });\r\n    }\r\n  }\r\n\r\n\r\n  // Initialize all slider backgrounds\r\n  initializeSliderBackgrounds() {\r\n    // Use requestAnimationFrame for better performance\r\n    requestAnimationFrame(() => {\r\n      const sliders = document.querySelectorAll('.progress-slider');\r\n      if (sliders.length === 0) {\r\n        return;\r\n      }\r\n\r\n      sliders.forEach(slider => {\r\n        if (slider instanceof HTMLInputElement) {\r\n          // Get the slider's quest ID for debugging\r\n          const sliderQuestId = slider.getAttribute('data-quest-id');\r\n          if (!sliderQuestId) {\r\n            return;\r\n          }\r\n\r\n          // Get the exact value from the slider (no rounding)\r\n          const sliderValue = parseInt(slider.value);\r\n          const minValue = parseInt(slider.min);\r\n          const maxValue = parseInt(slider.max);\r\n\r\n          // Calculate the percentage value\r\n          const percentage = maxValue > minValue ?\r\n            ((sliderValue - minValue) / (maxValue - minValue)) * 100 : 0;\r\n\r\n          // Set the background directly with hardcoded colors\r\n          slider.style.background =\r\n            `linear-gradient(to right, #4169E1 0%, #4169E1 ${percentage}%, #2C2C2E ${percentage}%, #2C2C2E 100%)`;\r\n\r\n          // Add a data attribute to track the current value\r\n          slider.setAttribute('data-current-value', slider.value);\r\n        } else if (slider instanceof HTMLElement && slider.tagName === 'ION-RANGE') {\r\n          // Get the slider's quest ID for debugging\r\n          const sliderQuestId = slider.getAttribute('data-quest-id');\r\n          if (!sliderQuestId) {\r\n            return;\r\n          }\r\n\r\n          // Get the value from the element's properties or attributes\r\n          const valueAttr = slider.getAttribute('value') || '0';\r\n          const minAttr = slider.getAttribute('min') || '0';\r\n          const maxAttr = slider.getAttribute('max') || '100';\r\n\r\n          const sliderValue = parseInt(valueAttr);\r\n          const minValue = parseInt(minAttr);\r\n          const maxValue = parseInt(maxAttr);\r\n\r\n          // Calculate the percentage value\r\n          const percentage = maxValue > minValue ?\r\n            ((sliderValue - minValue) / (maxValue - minValue)) * 100 : 0;\r\n\r\n          // Set the CSS variable for the progress\r\n          slider.style.setProperty('--progress-value', `${percentage}%`);\r\n\r\n          // Add a data attribute to track the current value\r\n          slider.setAttribute('data-current-value', sliderValue.toString());\r\n        }\r\n      });\r\n    });\r\n  }\r\n\r\n  ionViewWillLeave() {\r\n    console.log('TodayPage: ionViewWillLeave called');\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    console.log('TodayPage: ngOnDestroy called');\r\n    if (this.userSubscription) {\r\n      this.userSubscription.unsubscribe();\r\n    }\r\n  }\r\n\r\n  async loadData() {\r\n    if (!this.userId) {\r\n      return;\r\n    }\r\n\r\n    // Update header text\r\n    this.updateHeaderText();\r\n\r\n    // Check if we have cached data for this date\r\n    const dateKey = this.formatDate(this.selectedDate);\r\n    if (this.questCache[dateKey]) {\r\n      // Use cached data\r\n      this.quests = this.questCache[dateKey];\r\n\r\n      // Initialize slider backgrounds immediately\r\n      requestAnimationFrame(() => {\r\n        this.initializeSliderBackgrounds();\r\n      });\r\n\r\n      // Load daily side quest if needed\r\n      this.loadDailySideQuest();\r\n\r\n      return;\r\n    }\r\n\r\n    // Set up date variables\r\n    const today = new Date();\r\n    today.setHours(0, 0, 0, 0);\r\n    const selectedDate = new Date(this.selectedDate);\r\n    selectedDate.setHours(0, 0, 0, 0);\r\n    const isTodaySelected = selectedDate.getTime() === today.getTime();\r\n\r\n    console.log('TodayPage: Loading data for date:', this.formatDate(this.selectedDate));\r\n    if (isTodaySelected) {\r\n      // Check if we've already calculated streaks for today\r\n      const todayDateString = this.formatDate(today);\r\n      try {\r\n        const { value: lastStreakCalculation } = await this.preferencesService.get('last_streak_calculation');\r\n\r\n        if (lastStreakCalculation !== todayDateString) {\r\n          console.log('TodayPage: First time loading today, calculating streaks');\r\n\r\n          // Najprv spracujeme všetky questy pomocou checkMissedDays\r\n          await firstValueFrom(this.questService.getQuests(this.userId!).pipe(\r\n            take(1),\r\n            switchMap(async quests => {\r\n              // Check missed days for each quest\r\n              for (const quest of quests) {\r\n                if (quest.id) {\r\n                  await this.questService.checkMissedDays(quest.id);\r\n                }\r\n              }\r\n\r\n              // Potom vytvoríme progress záznamy pre quit questy\r\n              await this.questService.createQuitQuestProgressForToday();\r\n\r\n              // NEBUDEME tu nastavovať last_streak_calculation, aby sa mohli vypočítať streaky v ďalšej časti kódu\r\n              return quests;\r\n            })\r\n          ));\r\n\r\n          // Streaky sa vypočítajú v ďalšej časti kódu\r\n        } else {\r\n          console.log('TodayPage: Streaks already calculated for today');\r\n        }\r\n      } catch (error) {\r\n        console.error('TodayPage: Error checking last streak calculation:', error);\r\n\r\n        // Ak nastane chyba, nenastavujeme last_streak_calculation, aby sa mohli vypočítať streaky\r\n      }\r\n\r\n      // Recalculate streak for the daily side quest only for today\r\n      if (this.showSidequests) {\r\n        this.sideQuestService.recalculateSideQuestStreak(this.userId, this.selectedDate)\r\n          .subscribe({\r\n            error: (error) => {\r\n              console.error('Error recalculating side quest streak:', error);\r\n            }\r\n          });\r\n      }\r\n    }\r\n    // Load quests\r\n    this.questService.getQuests(this.userId).pipe(\r\n      take(1),\r\n      switchMap(quests => {\r\n        // Filter active quests for the selected date\r\n        const filteredQuests = this.filterQuestsForDate(quests, this.selectedDate);\r\n\r\n        if (filteredQuests.length === 0) {\r\n          return of([]);\r\n        }\r\n\r\n        // Sort filtered quests by creation date (newest first) or ID\r\n        const sortedFilteredQuests = [...filteredQuests].sort((a, b) => {\r\n          if (a.created_at && b.created_at) {\r\n            return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();\r\n          }\r\n          return a.id && b.id ? a.id.localeCompare(b.id) : 0;\r\n        });\r\n\r\n        // Get all progress for all quests at once\r\n        return this.questService.getQuestProgressForDate(this.userId!, this.selectedDate).pipe(\r\n          take(1),\r\n          switchMap(allProgress => {\r\n            // Create a lookup for quick access\r\n            const progressLookup: { [questId: string]: QuestProgress } = {};\r\n            allProgress.forEach(progress => {\r\n              progressLookup[progress.quest_id] = progress;\r\n            });\r\n\r\n            // For today's view, calculate streaks once per day\r\n            // For other days, just use the streak from the database\r\n            if (isTodaySelected) {\r\n              // Check if we've already calculated streaks for today\r\n              const todayDateString = this.formatDate(today);\r\n              return this.preferencesService.get('last_streak_calculation').then(({ value: lastStreakCalculation }) => {\r\n                if (lastStreakCalculation !== todayDateString) {\r\n                  console.log('TodayPage: First time loading today, calculating streaks');\r\n\r\n                  // Calculate streaks using our streak calculator\r\n                  return this.streakCalculator.calculateStreaks(this.userId!, sortedFilteredQuests).then(streaks => {\r\n                    // Map quests with progress and calculated streaks\r\n                    return sortedFilteredQuests.map(quest => {\r\n                      const progress = progressLookup[quest.id!];\r\n                      const calculatedStreak = streaks[quest.id!] || 0;\r\n\r\n                      // Update the streak in the database\r\n                      this.questService.updateQuestStreak(quest.id!, calculatedStreak).subscribe();\r\n\r\n                      return {\r\n                        ...quest,\r\n                        completed: progress?.completed || false,\r\n                        value_achieved: progress?.value_achieved || 0,\r\n                        streak: calculatedStreak\r\n                      } as QuestDisplay;\r\n                    });\r\n                  }).then(result => {\r\n                    // Po výpočte streakov nastavíme last_streak_calculation\r\n                    this.preferencesService.set('last_streak_calculation', todayDateString);\r\n                    return result;\r\n                  });\r\n                } else {\r\n                  console.log('TodayPage: Streaks already calculated for today, using database values');\r\n\r\n                  // Just use the streak from the database\r\n                  return sortedFilteredQuests.map(quest => {\r\n                    const progress = progressLookup[quest.id!];\r\n\r\n                    return {\r\n                      ...quest,\r\n                      completed: progress?.completed || false,\r\n                      value_achieved: progress?.value_achieved || 0,\r\n                      streak: quest.streak || 0\r\n                    } as QuestDisplay;\r\n                  });\r\n                }\r\n              }).catch(error => {\r\n                console.error('TodayPage: Error checking last streak calculation:', error);\r\n\r\n                // If there's an error, just use the streak from the database\r\n                return sortedFilteredQuests.map(quest => {\r\n                  const progress = progressLookup[quest.id!];\r\n\r\n                  return {\r\n                    ...quest,\r\n                    completed: progress?.completed || false,\r\n                    value_achieved: progress?.value_achieved || 0,\r\n                    streak: quest.streak || 0\r\n                  } as QuestDisplay;\r\n                });\r\n              });\r\n            } else {\r\n              // For previous days, just use the streak from the database but set it to 0 for display\r\n              return Promise.resolve(sortedFilteredQuests.map(quest => {\r\n                const progress = progressLookup[quest.id!];\r\n\r\n                return {\r\n                  ...quest,\r\n                  completed: progress?.completed || false,\r\n                  value_achieved: progress?.value_achieved || 0,\r\n                  streak: 0 // Don't show streak for previous days\r\n                } as QuestDisplay;\r\n              }));\r\n            }\r\n          })\r\n        );\r\n\r\n\r\n      })\r\n    ).subscribe({\r\n      next: (questsWithProgress) => {\r\n        // Sort quests by creation date (newest first) or ID\r\n        const sortedQuests = [...questsWithProgress].sort((a, b) => {\r\n          if (a.created_at && b.created_at) {\r\n            return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();\r\n          }\r\n          return a.id && b.id ? a.id.localeCompare(b.id) : 0;\r\n        });\r\n\r\n        // Check if all quests are completed for today\r\n        this.checkAllQuestsCompleted(sortedQuests);\r\n\r\n        // Update the quests array\r\n        this.quests = sortedQuests;\r\n\r\n        // Cache the quests for this date\r\n        const dateKey = this.formatDate(this.selectedDate);\r\n        this.questCache[dateKey] = sortedQuests;\r\n\r\n        // Update the week date progress\r\n        this.updateWeekDateProgress();\r\n\r\n        // Initialize slider backgrounds\r\n        requestAnimationFrame(() => {\r\n          this.initializeSliderBackgrounds();\r\n        });\r\n\r\n        // Load daily side quest if needed\r\n        this.loadDailySideQuest();\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading quests:', error);\r\n      }\r\n    });\r\n\r\n  }\r\n\r\n  generateWeekDates() {\r\n    const today = new Date();\r\n\r\n    // Calculate the start of the week based on week offset\r\n    // This starts on Monday (1) instead of Sunday (0)\r\n    const currentDay = today.getDay(); // 0 = Sunday, 6 = Saturday\r\n    const daysFromMonday = currentDay === 0 ? 6 : currentDay - 1; // Convert to Monday-based (0 = Monday)\r\n    const startOfWeek = new Date(today);\r\n    startOfWeek.setDate(today.getDate() - daysFromMonday + (7 * this.weekOffset));\r\n\r\n    this.weekDates = [];\r\n    for (let i = 0; i < 7; i++) {\r\n      const date = new Date(startOfWeek);\r\n      date.setDate(startOfWeek.getDate() + i);\r\n\r\n      const dateString = this.formatDate(date);\r\n      const isToday = this.isSameDay(date, today);\r\n      const isSelected = this.isSameDay(date, this.selectedDate);\r\n      const isFuture = date > today;\r\n\r\n      // Check if we have cached progress for this date\r\n      const dateKey = dateString;\r\n      let totalQuests = 0;\r\n      let completedQuests = 0;\r\n      let completionPercentage = 0;\r\n\r\n      if (this.weekProgressCache[dateKey]) {\r\n        const cached = this.weekProgressCache[dateKey];\r\n        totalQuests = cached.total;\r\n        completedQuests = cached.completed;\r\n        completionPercentage = totalQuests > 0\r\n          ? Math.round((completedQuests / totalQuests) * 100)\r\n          : 0;\r\n      }\r\n\r\n      this.weekDates.push({\r\n        date: dateString,\r\n        day: date.getDate(),\r\n        is_today: isToday,\r\n        is_selected: isSelected,\r\n        is_future: isFuture,\r\n        total_quests: totalQuests,\r\n        completed_quests: completedQuests,\r\n        completion_percentage: completionPercentage\r\n      });\r\n    }\r\n\r\n    // Preload data for all days in the week\r\n    if (this.userId) {\r\n      // Use setTimeout to allow the UI to render first\r\n      setTimeout(() => {\r\n        this.preloadWeekData();\r\n      }, 0);\r\n    }\r\n  }\r\n\r\n  // Cache for week date progress\r\n  private weekProgressCache: { [dateKey: string]: { total: number, completed: number } } = {};\r\n\r\n  updateWeekDateProgress() {\r\n    if (!this.userId) return;\r\n\r\n    // For each date in the week, update the progress\r\n    this.weekDates.forEach((weekDate, index) => {\r\n      if (weekDate.is_future) return;\r\n\r\n      const date = new Date(weekDate.date);\r\n      const dateKey = this.formatDate(date);\r\n\r\n      // Check if we have cached progress for this date\r\n      if (this.weekProgressCache[dateKey]) {\r\n        const cached = this.weekProgressCache[dateKey];\r\n        this.weekDates[index].total_quests = cached.total;\r\n        this.weekDates[index].completed_quests = cached.completed;\r\n        this.weekDates[index].completion_percentage = cached.total > 0\r\n          ? Math.round((cached.completed / cached.total) * 100)\r\n          : 0;\r\n        return;\r\n      }\r\n\r\n      // If we have cached quests for this date, use them to calculate progress\r\n      if (this.questCache[dateKey]) {\r\n        const cachedQuests = this.questCache[dateKey];\r\n        const totalQuests = cachedQuests.length;\r\n        const completedQuests = cachedQuests.filter(q => q.completed).length;\r\n\r\n        // Cache the progress\r\n        this.weekProgressCache[dateKey] = {\r\n          total: totalQuests,\r\n          completed: completedQuests\r\n        };\r\n\r\n        // Update the week date\r\n        this.weekDates[index].total_quests = totalQuests;\r\n        this.weekDates[index].completed_quests = completedQuests;\r\n        this.weekDates[index].completion_percentage = totalQuests > 0\r\n          ? Math.round((completedQuests / totalQuests) * 100)\r\n          : 0;\r\n        return;\r\n      }\r\n    });\r\n\r\n    // Preload data for all days in the week\r\n    this.preloadWeekData();\r\n  }\r\n\r\n  // Helper method to filter quests for a specific date\r\n  private filterQuestsForDate(quests: Quest[], date: Date): Quest[] {\r\n    const dateObj = new Date(date);\r\n    const dayOfWeek = dateObj.getDay(); // 0 = Sunday, 1 = Monday, etc.\r\n    // Django uses Monday=0, Sunday=6 format, so we need to convert\r\n    const djangoDayOfWeek = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Convert to Django format\r\n    const dayOfMonth = dateObj.getDate(); // 1-31\r\n\r\n    console.log(`TodayPage: Filtering quests for date ${this.formatDate(date)}, day of week: ${dayOfWeek} (Django: ${djangoDayOfWeek}), day of month: ${dayOfMonth}`);\r\n\r\n    const filteredQuests = quests.filter(quest => {\r\n      console.log(`TodayPage: Checking quest ${quest.id} (${quest.name}), type: ${quest.quest_type}, period: ${quest.goal_period}, task_days_of_week: ${quest.task_days_of_week}, task_days_of_month: ${quest.task_days_of_month}`);\r\n\r\n      if (!quest.active) {\r\n        console.log(`TodayPage: Quest ${quest.id} (${quest.name}) is not active, filtering out`);\r\n        return false;\r\n      }\r\n\r\n      // Only show quests from the date they were created\r\n      if (quest.created_at) {\r\n        const createdDate = new Date(quest.created_at);\r\n        createdDate.setHours(0, 0, 0, 0);\r\n        dateObj.setHours(0, 0, 0, 0);\r\n\r\n        // If the selected date is before the quest was created, don't show it\r\n        if (dateObj < createdDate) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // Daily quests are always shown\r\n      if (quest.goal_period === 'day') {\r\n        return true;\r\n      }\r\n\r\n      // Weekly quests are shown on specific days\r\n      if (quest.goal_period === 'week') {\r\n        if (!quest.task_days_of_week) {\r\n          console.log(`TodayPage: Quest ${quest.id} (${quest.name}) has no task_days_of_week specified, showing every day`);\r\n          return true; // If no days specified, show every day\r\n        }\r\n\r\n        // Parse task_days_of_week\r\n        let taskDays: any[] = [];\r\n        if (typeof quest.task_days_of_week === 'string') {\r\n          taskDays = quest.task_days_of_week.split(',').map(day => day.trim());\r\n          console.log(`TodayPage: Quest ${quest.id} (${quest.name}) has task_days_of_week as string: ${quest.task_days_of_week}, parsed to:`, taskDays);\r\n        } else if (Array.isArray(quest.task_days_of_week)) {\r\n          taskDays = quest.task_days_of_week;\r\n          console.log(`TodayPage: Quest ${quest.id} (${quest.name}) has task_days_of_week as array:`, taskDays);\r\n        }\r\n\r\n        // Check if current day is in task days\r\n        // Convert current day to different formats for comparison\r\n        const dayNameShort = this.getDayNameShort(djangoDayOfWeek);\r\n        const dayNameFull = this.getDayNameFull(djangoDayOfWeek);\r\n\r\n        console.log(`TodayPage: Checking if day ${dayNameFull} (${dayNameShort}, ${djangoDayOfWeek}) is in task days:`, taskDays);\r\n\r\n        const isIncluded = taskDays.includes(djangoDayOfWeek) ||\r\n          taskDays.includes(djangoDayOfWeek.toString()) ||\r\n          taskDays.includes(dayNameShort) ||\r\n          taskDays.includes(dayNameFull);\r\n\r\n        console.log(`TodayPage: Quest ${quest.id} (${quest.name}) should be shown on ${dayNameFull}? ${isIncluded}`);\r\n\r\n        return isIncluded;\r\n      }\r\n\r\n      // Monthly quests are shown on specific days of month\r\n      if (quest.goal_period === 'month') {\r\n        if (!quest.task_days_of_month) return true; // If no days specified, show every day\r\n\r\n        // Parse task_days_of_month\r\n        let taskDays: any[] = [];\r\n        if (typeof quest.task_days_of_month === 'string') {\r\n          taskDays = quest.task_days_of_month.split(',').map(day => parseInt(day.trim()));\r\n        } else if (Array.isArray(quest.task_days_of_month)) {\r\n          taskDays = quest.task_days_of_month;\r\n        }\r\n\r\n        // Check if current day is in task days\r\n        return taskDays.includes(dayOfMonth) ||\r\n          taskDays.includes(dayOfMonth.toString());\r\n      }\r\n\r\n      return false;\r\n    });\r\n\r\n    console.log(`TodayPage: Filtered ${quests.length} quests to ${filteredQuests.length} for date ${this.formatDate(date)}`);\r\n\r\n    return filteredQuests;\r\n  }\r\n\r\n  selectDate(dateObj: any) {\r\n    if (this.isLoadingData) {\r\n      return;\r\n    }\r\n\r\n    const dateData = dateObj.date\r\n\r\n    this.isLoadingData = true;\r\n\r\n    this.selectedDateData = dateObj;\r\n\r\n    const date = new Date(dateData);\r\n    this.selectedDate = date;\r\n\r\n    this.weekDates.forEach(weekDate => {\r\n      weekDate.is_selected = weekDate.date === dateData;\r\n    });\r\n\r\n    const formattedDate = this.formatDate(date);\r\n\r\n    this.router.navigate(['/today'], {\r\n      queryParams: {\r\n        date: formattedDate,\r\n        week_offset: this.weekOffset !== 0 ? this.weekOffset : null\r\n      },\r\n      replaceUrl: true\r\n    });\r\n\r\n    this.updateHeaderText();\r\n\r\n    setTimeout(() => {\r\n      this.loadData();\r\n      this.isLoadingData = false;\r\n    }, 10);\r\n  }\r\n\r\n  // Flag to track if we're currently changing weeks\r\n  private isChangingWeek = false;\r\n\r\n  changeWeek(direction: number) {\r\n    // Prevent multiple rapid week changes\r\n    if (this.isChangingWeek) {\r\n      return;\r\n    }\r\n\r\n    this.isChangingWeek = true;\r\n\r\n    // Update the week offset\r\n    this.weekOffset += direction;\r\n\r\n    // Generate new week dates with the updated offset\r\n    this.generateWeekDates();\r\n\r\n    // Preload quest data for all days in the week\r\n    this.preloadWeekData();\r\n\r\n    // Update the URL with the new week offset while preserving the selected date\r\n    const dateParam = this.formatDate(this.selectedDate);\r\n    this.router.navigate(['/today'], {\r\n      queryParams: {\r\n        date: dateParam,\r\n        week_offset: this.weekOffset\r\n      },\r\n      replaceUrl: true\r\n    });\r\n\r\n    // Reset the flag after a short delay\r\n    setTimeout(() => {\r\n      this.isChangingWeek = false;\r\n    }, 300);\r\n  }\r\n\r\n  // Preload data for all days in the current week\r\n  private preloadWeekData() {\r\n    if (!this.userId) return;\r\n\r\n    // Get all quests once to avoid multiple API calls\r\n    this.questService.getQuests(this.userId!).pipe(\r\n      take(1)\r\n    ).subscribe(allQuests => {\r\n      // Create an array of observables for each date\r\n      const dateObservables = this.weekDates\r\n        .filter(weekDate => !weekDate.is_future)\r\n        .map(weekDate => {\r\n          const date = new Date(weekDate.date);\r\n          const dateKey = this.formatDate(date);\r\n\r\n          // Skip if we already have cached data\r\n          if (this.weekProgressCache[dateKey]) {\r\n            return of({\r\n              date: weekDate.date,\r\n              progress: this.weekProgressCache[dateKey]\r\n            });\r\n          }\r\n\r\n          // Filter active quests for this date\r\n          const activeQuests = this.filterQuestsForDate(allQuests, date);\r\n\r\n          // If no active quests, return empty progress\r\n          if (activeQuests.length === 0) {\r\n            const emptyProgress = { total: 0, completed: 0 };\r\n            this.weekProgressCache[dateKey] = emptyProgress;\r\n            return of({\r\n              date: weekDate.date,\r\n              progress: emptyProgress\r\n            });\r\n          }\r\n\r\n          // Get progress for this date\r\n          return this.questService.getQuestProgressForDate(this.userId!, date).pipe(\r\n            take(1),\r\n            map(progressList => {\r\n              // Count completed quests\r\n              const questIds = activeQuests.map(q => q.id);\r\n              const relevantProgress = progressList.filter(p => questIds.includes(p.quest_id));\r\n              const completedQuests = relevantProgress.filter(p => p.completed).length;\r\n              const totalQuests = activeQuests.length;\r\n\r\n              // Create progress object\r\n              const progress = {\r\n                total: totalQuests,\r\n                completed: completedQuests\r\n              };\r\n\r\n              // Cache the progress\r\n              this.weekProgressCache[dateKey] = progress;\r\n\r\n              return {\r\n                date: weekDate.date,\r\n                progress\r\n              };\r\n            })\r\n          );\r\n        });\r\n\r\n      // Process all date observables in parallel\r\n      forkJoin(dateObservables).subscribe(results => {\r\n        // Update the week dates with the progress\r\n        results.forEach(result => {\r\n          const index = this.weekDates.findIndex(wd => wd.date === result.date);\r\n          if (index >= 0) {\r\n            this.weekDates[index].total_quests = result.progress.total;\r\n            this.weekDates[index].completed_quests = result.progress.completed;\r\n            this.weekDates[index].completion_percentage = result.progress.total > 0\r\n              ? Math.round((result.progress.completed / result.progress.total) * 100)\r\n              : 0;\r\n          }\r\n        });\r\n      });\r\n    });\r\n  }\r\n\r\n  updateHeaderText() {\r\n    const today = new Date();\r\n    if (this.isSameDay(this.selectedDate, today)) {\r\n      this.headerText = 'Today';\r\n    } else if (this.isSameDay(this.selectedDate, new Date(today.setDate(today.getDate() - 1)))) {\r\n      this.headerText = 'Yesterday';\r\n    } else if (this.isSameDay(this.selectedDate, new Date(today.setDate(today.getDate() + 2)))) {\r\n      this.headerText = 'Tomorrow';\r\n    } else {\r\n      // Format as \"Mon, 15 Jan\"\r\n      this.headerText = this.selectedDate.toLocaleDateString('en-US', {\r\n        weekday: 'short',\r\n        day: 'numeric',\r\n        month: 'short'\r\n      });\r\n    }\r\n  }\r\n\r\n  // Map to track which quests are currently being toggled\r\n  private togglingQuestIds: { [questId: string]: boolean } = {};\r\n\r\n  async toggleQuest(quest: QuestDisplay) {\r\n    if (!this.userId || !quest.id) return;\r\n\r\n    // Check if this specific quest is already being toggled\r\n    if (this.togglingQuestIds[quest.id]) {\r\n      console.log(`TodayPage: Quest ${quest.id} (${quest.name}) is already being toggled, ignoring duplicate call`);\r\n      return;\r\n    }\r\n\r\n    // Set flag for this specific quest\r\n    this.togglingQuestIds[quest.id] = true;\r\n    console.log(`TodayPage: Starting toggle for quest ${quest.id} (${quest.name})`);\r\n\r\n    try {\r\n      // For normal quests, we don't want to toggle the value when clicking on the quest\r\n      // Instead, we want to keep the current value from the slider\r\n      // This is different from the original behavior where clicking would toggle between 0 and goal_value\r\n\r\n      // We'll just log that the quest was clicked but not change any values\r\n      console.log(`TodayPage: Quest ${quest.id} (${quest.name}) clicked, keeping current value: ${quest.value_achieved}`);\r\n\r\n      // No need to update the database since we're not changing any values\r\n      // Just release the flag and return\r\n      delete this.togglingQuestIds[quest.id];\r\n      return;\r\n    } catch (error) {\r\n      console.error(`TodayPage: Error in toggleQuest for ${quest.id} (${quest.name}):`, error);\r\n    } finally {\r\n      // Reset flag for this specific quest\r\n      delete this.togglingQuestIds[quest.id];\r\n      console.log(`TodayPage: Finished toggle for quest ${quest.id} (${quest.name})`);\r\n    }\r\n  }\r\n\r\n  // Map to track which quests are currently being updated\r\n  private updatingQuestIds: { [questId: string]: boolean } = {};\r\n\r\n  async updateQuestProgress(quest: QuestDisplay, event?: any) {\r\n    if (!this.userId || !quest.id) return;\r\n\r\n    // Check if this specific quest is already being updated\r\n    if (this.updatingQuestIds[quest.id]) {\r\n      return;\r\n    }\r\n\r\n    // Set flag for this specific quest\r\n    this.updatingQuestIds[quest.id] = true;\r\n\r\n    try {\r\n      // Store the original completed state before any changes\r\n      const wasCompletedBefore = quest.completed;\r\n      console.log(`TodayPage: Quest ${quest.id} (${quest.name}) original completed state: ${wasCompletedBefore}`);\r\n\r\n      // Update the slider background if an event is provided\r\n      if (event) {\r\n        // Handle both standard Event and Ionic's CustomEvent\r\n        const slider = event.target || (event.detail ? event.detail.value : null);\r\n        this.updateSliderBackground(slider);\r\n\r\n        // Verify that the slider is for the correct quest\r\n        const sliderQuestId = slider instanceof HTMLElement ? slider.getAttribute('data-quest-id') : null;\r\n        if (sliderQuestId && sliderQuestId !== quest.id) {\r\n          delete this.updatingQuestIds[quest.id];\r\n          return;\r\n        }\r\n\r\n        // Get the value from the slider\r\n        let sliderValue = 0;\r\n        if (event.detail && event.detail.value !== undefined) {\r\n          // Ionic range event\r\n          sliderValue = event.detail.value;\r\n        } else if (slider instanceof HTMLInputElement) {\r\n          // Standard input event\r\n          sliderValue = parseInt(slider.value);\r\n        } else if (slider instanceof HTMLElement && slider.tagName === 'ION-RANGE') {\r\n          // Ionic range element\r\n          const valueAttr = slider.getAttribute('value') || '0';\r\n          sliderValue = parseInt(valueAttr);\r\n        }\r\n\r\n        // Update the quest's value_achieved with the slider value\r\n        quest.value_achieved = sliderValue;\r\n\r\n        // Update completed status based on quest type and value\r\n        // This exactly matches the Django implementation in toggle_quest view\r\n        if (quest.quest_type === 'build') {\r\n          // For build quests, completed when value >= goal\r\n          quest.completed = sliderValue >= quest.goal_value;\r\n        } else { // 'quit' type\r\n          // For quit quests, completed when value < goal (opposite of build)\r\n          quest.completed = sliderValue < quest.goal_value;\r\n        }\r\n\r\n        console.log(`TodayPage: Quest ${quest.id} (${quest.name}) new completed state: ${quest.completed}`);\r\n      }\r\n\r\n      // Make a deep copy of the quest to avoid reference issues\r\n      const questCopy = { ...quest };\r\n\r\n      // Call the service and get the updated values\r\n      const result = await this.questService.toggleQuestCompletion(\r\n        this.userId,\r\n        quest.id,\r\n        this.selectedDate,\r\n        quest.value_achieved,\r\n        questCopy\r\n      );\r\n\r\n      // Update the quest in the UI with the returned values\r\n      quest.completed = result.completed;\r\n      quest.value_achieved = result.value_achieved;\r\n\r\n      // Get today's date for comparison\r\n      const today = new Date();\r\n      today.setHours(0, 0, 0, 0);\r\n      const selectedDate = new Date(this.selectedDate);\r\n      selectedDate.setHours(0, 0, 0, 0);\r\n      const isTodaySelected = selectedDate.getTime() === today.getTime();\r\n\r\n      // Handle streak calculation differently based on whether we're in today's view or a previous day\r\n      if (isTodaySelected) {\r\n        // For today's view, manually calculate the streak by going backward from today\r\n        // until we find a non-completed progress entry\r\n\r\n        // Use the streak from the result (from Supabase)\r\n        let streak = result.streak;\r\n\r\n        // Get the current completed state after the update\r\n        const isCompletedNow = quest.completed;\r\n\r\n        console.log(`TodayPage: Quest ${quest.id} (${quest.name}) completion status: was ${wasCompletedBefore}, now ${isCompletedNow}`);\r\n\r\n        // Only update streak if the completion status has changed\r\n        if (wasCompletedBefore !== isCompletedNow) {\r\n          if (isCompletedNow) {\r\n            // Changed from incomplete to complete\r\n            streak++;\r\n            console.log(`TodayPage: Quest ${quest.id} (${quest.name}) changed from incomplete to complete, streak increased to ${streak}`);\r\n          } else {\r\n            // Changed from complete to incomplete\r\n            streak = Math.max(0, streak - 1);\r\n            console.log(`TodayPage: Quest ${quest.id} (${quest.name}) changed from complete to incomplete, streak decreased to ${streak}`);\r\n          }\r\n\r\n          // Update the streak in the database\r\n          this.questService.updateQuestStreak(quest.id!, streak).subscribe({\r\n            next: () => {\r\n              console.log(`TodayPage: Successfully updated streak for quest ${quest.id} to ${streak}`);\r\n\r\n              // Update the quest in the cache\r\n              const dateKey = this.formatDate(this.selectedDate);\r\n              if (this.questCache[dateKey]) {\r\n                const cachedQuestIndex = this.questCache[dateKey].findIndex(q => q.id === quest.id);\r\n                if (cachedQuestIndex >= 0) {\r\n                  this.questCache[dateKey][cachedQuestIndex].streak = streak;\r\n                }\r\n              }\r\n            },\r\n            error: (error: any) => console.error(`TodayPage: Error updating streak for quest ${quest.id}:`, error)\r\n          });\r\n        } else {\r\n          console.log(`TodayPage: Quest ${quest.id} (${quest.name}) completion status did not change, keeping streak at ${streak}`);\r\n        }\r\n      } else {\r\n        // For previous days, recalculate streak for today\r\n        console.log(`TodayPage: Quest toggled in previous day (${this.formatDate(this.selectedDate)}), recalculating streak for today`);\r\n\r\n        // Get the quest details\r\n        this.questService.getQuest(quest.id!).subscribe(questDetails => {\r\n          if (!questDetails) {\r\n            console.error(`TodayPage: Could not get quest details for ${quest.id}`);\r\n            return;\r\n          }\r\n\r\n          // Calculate the streak for today using our streak calculator\r\n          this.streakCalculator.calculateStreak(this.userId!, quest.id!)\r\n            .then(calculatedStreak => {\r\n              console.log(`TodayPage: Recalculated streak for quest ${quest.id} for today: ${calculatedStreak}`);\r\n\r\n              // Update the streak in the database\r\n              this.questService.updateQuestStreak(quest.id!, calculatedStreak).subscribe({\r\n                next: () => {\r\n                  console.log(`TodayPage: Successfully updated streak for quest ${quest.id} to ${calculatedStreak}`);\r\n\r\n                  // Clear today's cache for next time\r\n                  const todayString = this.formatDate(today);\r\n                  console.log('TodayPage: Clearing today\\'s cache to force reload of updated streak next time today is viewed');\r\n                  delete this.questCache[todayString];\r\n\r\n                  // If we have today's date in the week view, update its progress\r\n                  const todayIndex = this.weekDates.findIndex(wd => wd.date === todayString);\r\n                  if (todayIndex >= 0) {\r\n                    delete this.weekProgressCache[todayString];\r\n                    this.updateProgressRingForDate(todayString);\r\n                  }\r\n                },\r\n                error: (error: any) => console.error(`TodayPage: Error updating streak for quest ${quest.id}:`, error)\r\n              });\r\n            })\r\n            .catch(error => {\r\n              console.error(`TodayPage: Error calculating streak for quest ${quest.id}:`, error);\r\n            });\r\n        });\r\n      }\r\n\r\n      // Update the UI element for this quest\r\n      this.updateQuestUI(quest);\r\n\r\n      // Cache the updated quest data and update progress ring\r\n      const dateKey = this.formatDate(this.selectedDate);\r\n      if (this.questCache[dateKey]) {\r\n        // Find and update the quest in the cache\r\n        const cachedQuestIndex = this.questCache[dateKey].findIndex(q => q.id === quest.id);\r\n        if (cachedQuestIndex >= 0) {\r\n          this.questCache[dateKey][cachedQuestIndex] = { ...quest };\r\n        }\r\n      }\r\n\r\n      // Clear the cache for this date to force a refresh\r\n      delete this.weekProgressCache[dateKey];\r\n\r\n      // Update the progress ring for this date\r\n      this.updateProgressRingForDate(dateKey);\r\n\r\n      // Check if all quests are completed\r\n      this.checkAllQuestsCompleted(this.quests);\r\n    } catch (error) {\r\n      console.error(`TodayPage: Error updating quest progress:`, error);\r\n    } finally {\r\n      // Reset flag for this specific quest\r\n      delete this.updatingQuestIds[quest.id];\r\n    }\r\n  }\r\n\r\n  // Helper method to update the progress ring for a specific date\r\n  private updateProgressRingForDate(dateKey: string) {\r\n    // Find the index of this date in weekDates\r\n    const index = this.weekDates.findIndex(wd => wd.date === dateKey);\r\n    if (index < 0) return;\r\n\r\n    // If we have cached quests for this date, use them to calculate progress\r\n    if (this.questCache[dateKey]) {\r\n      const cachedQuests = this.questCache[dateKey];\r\n      const totalQuests = cachedQuests.length;\r\n      const completedQuests = cachedQuests.filter(q => q.completed).length;\r\n\r\n      // Cache the progress\r\n      this.weekProgressCache[dateKey] = {\r\n        total: totalQuests,\r\n        completed: completedQuests\r\n      };\r\n\r\n      // Update the week date\r\n      this.weekDates[index].total_quests = totalQuests;\r\n      this.weekDates[index].completed_quests = completedQuests;\r\n      this.weekDates[index].completion_percentage = totalQuests > 0\r\n        ? Math.round((completedQuests / totalQuests) * 100)\r\n        : 0;\r\n\r\n      return;\r\n    }\r\n\r\n    // If no cached quests, fetch from server\r\n    if (this.userId) {\r\n      const date = new Date(dateKey);\r\n\r\n      this.questService.getQuestProgressForDate(this.userId, date).pipe(\r\n        take(1)\r\n      ).subscribe(progressList => {\r\n        this.questService.getQuests(this.userId!).pipe(\r\n          take(1)\r\n        ).subscribe(quests => {\r\n          // Filter active quests for this date\r\n          const activeQuests = this.filterQuestsForDate(quests, date);\r\n\r\n          // Count completed quests\r\n          const questIds = activeQuests.map(q => q.id);\r\n          const relevantProgress = progressList.filter(p => questIds.includes(p.quest_id));\r\n          const completedQuests = relevantProgress.filter(p => p.completed).length;\r\n          const totalQuests = activeQuests.length;\r\n\r\n          // Cache the progress\r\n          this.weekProgressCache[dateKey] = {\r\n            total: totalQuests,\r\n            completed: completedQuests\r\n          };\r\n\r\n          // Update the week date\r\n          this.weekDates[index].total_quests = totalQuests;\r\n          this.weekDates[index].completed_quests = completedQuests;\r\n          this.weekDates[index].completion_percentage = totalQuests > 0\r\n            ? Math.round((completedQuests / totalQuests) * 100)\r\n            : 0;\r\n        });\r\n      });\r\n    }\r\n  }\r\n\r\n  // Helper method to update the UI for a specific quest\r\n  private updateQuestUI(quest: QuestDisplay) {\r\n    // Find the quest element in the DOM\r\n    const questElement = document.querySelector(`[data-quest-id=\"${quest.id}\"]`);\r\n    if (!questElement) {\r\n      console.error(`TodayPage: Could not find quest element for ID: ${quest.id}`);\r\n      return;\r\n    }\r\n\r\n    // Update the completed class\r\n    if (quest.completed) {\r\n      questElement.classList.add('completed');\r\n    } else {\r\n      questElement.classList.remove('completed');\r\n    }\r\n\r\n    // Update the streak display - only show streak for today\r\n    const streakElements = questElement.querySelectorAll('.quest-streak');\r\n    if (streakElements && streakElements.length > 0) {\r\n      // Get today's date for comparison\r\n      const today = new Date();\r\n      today.setHours(0, 0, 0, 0);\r\n      const selectedDate = new Date(this.selectedDate);\r\n      selectedDate.setHours(0, 0, 0, 0);\r\n      const isTodaySelected = selectedDate.getTime() === today.getTime();\r\n\r\n      // Only show streak for today's view\r\n      if (isTodaySelected) {\r\n        const streakValue = quest.streak || 0;\r\n        console.log(`TodayPage: Quest ${quest.id}, completed: ${quest.completed}, streak: ${streakValue}`);\r\n\r\n        // Update all streak elements (there might be multiple due to ngIf)\r\n        streakElements.forEach(element => {\r\n          if (element.parentElement && element.parentElement.contains(element)) {\r\n            // Make sure the streak is visible\r\n            (element as HTMLElement).style.display = 'block';\r\n            element.textContent = `🔥${streakValue}d`;\r\n          }\r\n        });\r\n      } else {\r\n        // Hide streak for previous days\r\n        streakElements.forEach(element => {\r\n          if (element.parentElement && element.parentElement.contains(element)) {\r\n            (element as HTMLElement).style.display = 'none';\r\n            element.textContent = '';\r\n          }\r\n        });\r\n      }\r\n    }\r\n\r\n    // Update the progress text\r\n    const progressText = questElement.querySelector('.progress-text');\r\n    if (progressText) {\r\n      const isTimeUnit = progressText.parentElement?.classList.contains('progress-time');\r\n      const unitSuffix = isTimeUnit ? 'm' : '';\r\n      const goalUnitSuffix = quest.goal_unit !== 'count' && !isTimeUnit ? ` ${quest.goal_unit}` : '';\r\n\r\n      progressText.textContent = `${quest.value_achieved}${unitSuffix}/${quest.goal_value}${unitSuffix}${goalUnitSuffix}`;\r\n    }\r\n\r\n    console.log(`TodayPage: Updated UI for quest ${quest.id}`);\r\n  }\r\n\r\n  // Update slider background based on value\r\n  updateSliderBackground(slider: HTMLInputElement | EventTarget | null) {\r\n    if (!slider) {\r\n      return;\r\n    }\r\n\r\n    // Handle different types of slider elements\r\n    let sliderElement: HTMLElement;\r\n    let sliderValue = 0;\r\n    let minValue = 0;\r\n    let maxValue = 100;\r\n    let sliderQuestId = '';\r\n\r\n    if (slider instanceof HTMLInputElement) {\r\n      // Standard HTML input range\r\n      sliderElement = slider;\r\n      sliderQuestId = slider.getAttribute('data-quest-id') || '';\r\n      sliderValue = parseInt(slider.value);\r\n      minValue = parseInt(slider.min);\r\n      maxValue = parseInt(slider.max);\r\n    } else if (slider instanceof HTMLElement && slider.tagName === 'ION-RANGE') {\r\n      // Ionic range element\r\n      sliderElement = slider;\r\n      sliderQuestId = slider.getAttribute('data-quest-id') || '';\r\n\r\n      // Get the value from the element's properties or attributes\r\n      const valueAttr = slider.getAttribute('value') || '0';\r\n      const minAttr = slider.getAttribute('min') || '0';\r\n      const maxAttr = slider.getAttribute('max') || '100';\r\n\r\n      sliderValue = parseInt(valueAttr);\r\n      minValue = parseInt(minAttr);\r\n      maxValue = parseInt(maxAttr);\r\n    } else {\r\n      return;\r\n    }\r\n\r\n    if (!sliderQuestId) {\r\n      return;\r\n    }\r\n\r\n    // Calculate the percentage value\r\n    const percentage = maxValue > minValue ?\r\n      ((sliderValue - minValue) / (maxValue - minValue)) * 100 : 0;\r\n\r\n    // For Ionic range, we need to set the CSS variable\r\n    if (sliderElement.tagName === 'ION-RANGE') {\r\n      sliderElement.style.setProperty('--progress-value', `${percentage}%`);\r\n    } else {\r\n      // Set the background directly with hardcoded colors for standard HTML input\r\n      sliderElement.style.background =\r\n        `linear-gradient(to right, #4169E1 0%, #4169E1 ${percentage}%, #2C2C2E ${percentage}%, #2C2C2E 100%)`;\r\n    }\r\n\r\n    // Add a data attribute to track the current value\r\n    sliderElement.setAttribute('data-current-value', sliderValue.toString());\r\n  }\r\n\r\n  // Map to track which side quests are currently being toggled\r\n  private togglingSideQuestIds: { [questId: string]: boolean } = {};\r\n\r\n  /**\r\n   * Toggle side quest completion\r\n   * This matches the Django implementation in toggle_daily_side_quest view\r\n   * Side quests are always toggled between 0 and goal value\r\n   */\r\n  async toggleSideQuest(sideQuest: DailyQuest) {\r\n    if (!this.userId || !sideQuest.id) return;\r\n\r\n    // Check if this specific side quest is already being toggled\r\n    if (this.togglingSideQuestIds[sideQuest.id]) {\r\n      console.log(`TodayPage: Side quest ${sideQuest.id} is already being toggled, ignoring duplicate call`);\r\n      return;\r\n    }\r\n\r\n    // Set flag for this specific side quest\r\n    this.togglingSideQuestIds[sideQuest.id] = true;\r\n    console.log(`TodayPage: Starting toggle for side quest ${sideQuest.id}`);\r\n\r\n    try {\r\n      // For side quests, we always toggle between 0 and goal value\r\n      // This matches the Django implementation where side quests are either completed or not\r\n      console.log(`TodayPage: Click event on side quest ${sideQuest.id}`);\r\n\r\n      // Toggle the value immediately for better UI feedback\r\n      const newValue = sideQuest.value_achieved === 0 ? sideQuest.current_quest.goal_value : 0;\r\n      const newCompletedState = newValue === sideQuest.current_quest.goal_value;\r\n\r\n      // Update local state first for immediate feedback\r\n      sideQuest.value_achieved = newValue;\r\n      sideQuest.completed = newCompletedState;\r\n\r\n      console.log(`TodayPage: Updated side quest ${sideQuest.id} value to ${sideQuest.value_achieved}, completed: ${sideQuest.completed}`);\r\n\r\n      // Get today's date for comparison\r\n      const today = new Date();\r\n      today.setHours(0, 0, 0, 0);\r\n      const selectedDate = new Date(this.selectedDate);\r\n      selectedDate.setHours(0, 0, 0, 0);\r\n      const isToday = selectedDate.getTime() === today.getTime();\r\n\r\n      // Only allow toggling side quests for today\r\n      if (!isToday) {\r\n        console.log(`TodayPage: Cannot toggle side quest for past date: ${this.formatDate(this.selectedDate)}`);\r\n        delete this.togglingSideQuestIds[sideQuest.id];\r\n        return;\r\n      }\r\n\r\n      // Update the UI element immediately for better feedback\r\n      this.updateSideQuestUI(sideQuest);\r\n\r\n      try {\r\n        const result = await this.sideQuestService.toggleSideQuestCompletion(\r\n          sideQuest.id,\r\n          this.userId,\r\n          this.selectedDate // Pass the selected date\r\n        );\r\n\r\n        console.log(`TodayPage: Successfully toggled side quest ${sideQuest.id}`);\r\n        console.log(`TodayPage: Updated values:`, result);\r\n\r\n        // Update the side quest in the UI with the returned values from the server\r\n        sideQuest.completed = result.completed;\r\n        sideQuest.value_achieved = result.value_achieved;\r\n        sideQuest.streak = result.streak;\r\n\r\n        // Update the UI element with the updated streak\r\n        this.updateSideQuestUI(sideQuest);\r\n\r\n        // Update the week date progress for the selected date\r\n        // Clear the cache for this date to force a refresh\r\n        const dateKey = this.formatDate(this.selectedDate);\r\n        delete this.weekProgressCache[dateKey];\r\n\r\n        // Update the progress ring for this date\r\n        this.updateProgressRingForDate(dateKey);\r\n\r\n        // Check if all quests are completed\r\n        this.checkAllQuestsCompleted(this.quests);\r\n\r\n        // Reset flag for this specific side quest\r\n        delete this.togglingSideQuestIds[sideQuest.id];\r\n        console.log(`TodayPage: Finished toggle for side quest ${sideQuest.id}`);\r\n      } catch (error) {\r\n        console.error(`TodayPage: Error toggling side quest ${sideQuest.id}:`, error);\r\n\r\n        // Revert the local state if the server update failed\r\n        sideQuest.value_achieved = sideQuest.value_achieved === 0 ? sideQuest.current_quest.goal_value : 0;\r\n        sideQuest.completed = sideQuest.value_achieved === sideQuest.current_quest.goal_value;\r\n        this.updateSideQuestUI(sideQuest);\r\n\r\n        delete this.togglingSideQuestIds[sideQuest.id];\r\n      }\r\n    } catch (error) {\r\n      console.error(`TodayPage: Error in toggleSideQuest for ${sideQuest.id}:`, error);\r\n      delete this.togglingSideQuestIds[sideQuest.id];\r\n    }\r\n  }\r\n\r\n  // Helper method to update the UI for a specific side quest\r\n  private updateSideQuestUI(sideQuest: DailyQuest) {\r\n    // Find the side quest element in the DOM\r\n    const questElement = document.querySelector(`.daily-side-quest [data-quest-id=\"${sideQuest.id}\"]`);\r\n    if (!questElement) {\r\n      console.error(`TodayPage: Could not find side quest element for ID: ${sideQuest.id}`);\r\n      return;\r\n    }\r\n\r\n    // Update the completed class\r\n    if (sideQuest.completed) {\r\n      questElement.classList.add('completed');\r\n    } else {\r\n      questElement.classList.remove('completed');\r\n    }\r\n\r\n    // Update the streak display - only show streak for today\r\n    const streakElements = questElement.querySelectorAll('.quest-streak');\r\n    if (streakElements && streakElements.length > 0) {\r\n      // Get today's date for comparison\r\n      const today = new Date();\r\n      today.setHours(0, 0, 0, 0);\r\n      const selectedDate = new Date(this.selectedDate);\r\n      selectedDate.setHours(0, 0, 0, 0);\r\n      const isTodaySelected = selectedDate.getTime() === today.getTime();\r\n\r\n      // Only show streak for today's view\r\n      if (isTodaySelected) {\r\n        const streakValue = sideQuest.streak || 0;\r\n        console.log(`TodayPage: Side quest ${sideQuest.id}, completed: ${sideQuest.completed}, streak: ${streakValue}`);\r\n\r\n        // Update all streak elements (there might be multiple due to ngIf)\r\n        streakElements.forEach(element => {\r\n          if (element.parentElement && element.parentElement.contains(element)) {\r\n            // Make sure the streak is visible\r\n            (element as HTMLElement).style.display = 'block';\r\n            element.textContent = `🔥${streakValue}d`;\r\n          }\r\n        });\r\n      } else {\r\n        // Hide streak for previous days\r\n        streakElements.forEach(element => {\r\n          if (element.parentElement && element.parentElement.contains(element)) {\r\n            (element as HTMLElement).style.display = 'none';\r\n            element.textContent = '';\r\n          }\r\n        });\r\n      }\r\n    }\r\n\r\n    // Update the progress text\r\n    const progressText = questElement.querySelector('.progress-text');\r\n    if (progressText) {\r\n      const goalUnit = sideQuest.current_quest.goal_unit !== 'count' ? ` ${sideQuest.current_quest.goal_unit}` : '';\r\n      progressText.textContent = `${sideQuest.value_achieved}/${sideQuest.current_quest.goal_value}${goalUnit}`;\r\n    }\r\n\r\n    // Force a repaint to ensure the UI updates\r\n    setTimeout(() => {\r\n      if (questElement.parentElement) {\r\n        const display = questElement.parentElement.style.display;\r\n        questElement.parentElement.style.display = 'none';\r\n        // Force a reflow\r\n        void questElement.parentElement.offsetHeight;\r\n        questElement.parentElement.style.display = display;\r\n      }\r\n    }, 0);\r\n\r\n    console.log(`TodayPage: Updated UI for side quest ${sideQuest.id}`);\r\n  }\r\n\r\n  openAddQuestModal(event: Event) {\r\n    event.preventDefault();\r\n    this.showAddQuestModal = true;\r\n    this.newQuest = this.getEmptyQuest();\r\n    this.selectedDaysOfWeek = [];\r\n    this.selectedDaysOfMonth = [];\r\n\r\n    // Reset hasHighPriorityQuest flag\r\n    this.hasHighPriorityQuest = false;\r\n  }\r\n\r\n  closeAddQuestModal() {\r\n    this.showAddQuestModal = false;\r\n    // Reset form state\r\n    this.newQuest = this.getEmptyQuest();\r\n    this.selectedDaysOfWeek = [];\r\n    this.selectedDaysOfMonth = [];\r\n    this.hasHighPriorityQuest = false;\r\n  }\r\n\r\n  async createQuest() {\r\n    if (!this.userId || !this.newQuest.name || !this.newQuest.emoji || !this.newQuest.quest_type ||\r\n      !this.newQuest.category || !this.newQuest.goal_value || !this.newQuest.goal_unit || !this.newQuest.goal_period) {\r\n      console.error('TodayPage: Cannot create quest - missing required fields');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Prepare days of week/month based on period\r\n      if (this.newQuest.goal_period === 'week' && this.selectedDaysOfWeek.length > 0) {\r\n        this.newQuest.task_days_of_week = this.selectedDaysOfWeek.join(',');\r\n      } else if (this.newQuest.goal_period === 'month' && this.selectedDaysOfMonth.length > 0) {\r\n        this.newQuest.task_days_of_month = this.selectedDaysOfMonth.join(',');\r\n      }\r\n\r\n      // First check if the user profile exists\r\n      console.log('TodayPage: Checking if user profile exists for ID:', this.userId);\r\n      const { data: userProfile, error: userError } = await this.supabaseService.getClient()\r\n        .from('profiles')\r\n        .select('id')\r\n        .eq('id', this.userId)\r\n        .single();\r\n\r\n      if (userError || !userProfile) {\r\n        console.error('TodayPage: User profile not found:', userError || 'No profile found');\r\n        throw new Error('User profile not found. Please ensure you are logged in.');\r\n      }\r\n\r\n      console.log('TodayPage: Found user profile:', userProfile);\r\n\r\n      // Add user_id and active status\r\n      // Note: user_id in the quest references profiles.id\r\n      const questToCreate: Omit<Quest, 'id' | 'streak' | 'created_at'> = {\r\n        name: this.newQuest.name || '',\r\n        description: this.newQuest.description || '',\r\n        quest_type: this.newQuest.quest_type || 'build',\r\n        goal_value: this.newQuest.goal_value || 1,\r\n        goal_unit: this.newQuest.goal_unit || 'count',\r\n        goal_period: this.newQuest.goal_period || 'day',\r\n        priority: this.newQuest.priority || 'basic',\r\n        category: this.newQuest.category || 'strength',\r\n        emoji: this.newQuest.emoji || '🎯',\r\n        task_days_of_week: this.newQuest.task_days_of_week || '',\r\n        task_days_of_month: this.newQuest.task_days_of_month || '',\r\n        user_id: this.userId, // This is profiles.id\r\n        active: true\r\n      };\r\n\r\n      console.log('TodayPage: Creating quest:', questToCreate);\r\n\r\n      try {\r\n        const questId = await this.questService.createQuest(questToCreate);\r\n        console.log('TodayPage: Quest created successfully with ID:', questId);\r\n\r\n        // For quit quests, create initial progress with completed=true and value_achieved=0\r\n        if (this.newQuest.quest_type === 'quit') {\r\n          console.log('TodayPage: Creating initial progress for quit quest with completed=true');\r\n\r\n          // Create initial progress for today\r\n          await this.questService.toggleQuestCompletion(\r\n            this.userId,\r\n            questId,\r\n            new Date(), // Today\r\n            0, // value_achieved = 0 for quit quests\r\n            { ...questToCreate, id: questId } as Quest\r\n          );\r\n        }\r\n\r\n        // Clear cache for the current date to force a refresh\r\n        const dateKey = this.formatDate(this.selectedDate);\r\n        delete this.questCache[dateKey];\r\n        delete this.weekProgressCache[dateKey];\r\n        console.log('TodayPage: Cleared cache for date:', dateKey);\r\n\r\n        this.closeAddQuestModal();\r\n        this.loadData();\r\n      } catch (questError: any) {\r\n        console.error('TodayPage: Error creating quest:', questError);\r\n\r\n        // Check if this is a foreign key constraint error\r\n        if (questError.message && questError.message.includes('foreign key constraint')) {\r\n          alert('Database configuration issue detected. Please run the fix_quest_constraints.sql script in the Supabase SQL Editor to fix the foreign key constraints.');\r\n        } else if (questError.message && questError.message.includes('fix_quest_constraints.sql')) {\r\n          alert(questError.message);\r\n        } else {\r\n          alert(`Error creating quest: ${questError.message}`);\r\n        }\r\n      }\r\n    } catch (error: any) {\r\n      console.error('TodayPage: Error in createQuest:', error);\r\n      alert(`Error: ${error.message || 'Unknown error occurred'}`);\r\n    }\r\n  }\r\n\r\n  updateDaysOfWeek(event: any, day: string) {\r\n    // Handle both standard Event and Ionic's CustomEvent\r\n    let isChecked = false;\r\n\r\n    if (event.detail !== undefined) {\r\n      // Ionic checkbox event\r\n      isChecked = event.detail.checked;\r\n    } else if (event.target instanceof HTMLInputElement) {\r\n      // Standard checkbox event\r\n      isChecked = event.target.checked;\r\n    }\r\n\r\n    if (isChecked) {\r\n      this.selectedDaysOfWeek.push(day);\r\n    } else {\r\n      const index = this.selectedDaysOfWeek.indexOf(day);\r\n      if (index !== -1) {\r\n        this.selectedDaysOfWeek.splice(index, 1);\r\n      }\r\n    }\r\n\r\n    console.log(`TodayPage: Updated days of week: ${this.selectedDaysOfWeek.join(', ')}`);\r\n  }\r\n\r\n  updateDaysOfMonth(event: any, day: number) {\r\n    // Handle both standard Event and Ionic's CustomEvent\r\n    let isChecked = false;\r\n\r\n    if (event.detail !== undefined) {\r\n      // Ionic checkbox event\r\n      isChecked = event.detail.checked;\r\n    } else if (event.target instanceof HTMLInputElement) {\r\n      // Standard checkbox event\r\n      isChecked = event.target.checked;\r\n    }\r\n\r\n    if (isChecked) {\r\n      this.selectedDaysOfMonth.push(day);\r\n    } else {\r\n      const index = this.selectedDaysOfMonth.indexOf(day);\r\n      if (index !== -1) {\r\n        this.selectedDaysOfMonth.splice(index, 1);\r\n      }\r\n    }\r\n\r\n    console.log(`TodayPage: Updated days of month: ${this.selectedDaysOfMonth.join(', ')}`);\r\n  }\r\n\r\n  updatePeriodDisplay() {\r\n    // Reset selections when period changes\r\n    this.selectedDaysOfWeek = [];\r\n    this.selectedDaysOfMonth = [];\r\n    console.log(`TodayPage: Period changed to ${this.newQuest.goal_period}, reset selections`);\r\n  }\r\n\r\n  checkCategoryPriority(event?: any) {\r\n    if (!this.userId || !this.newQuest.category) return;\r\n\r\n    // If this is an Ionic event, make sure we have the latest category value\r\n    if (event && event.detail) {\r\n      this.newQuest.category = event.detail.value;\r\n      console.log(`TodayPage: Category changed to ${this.newQuest.category} via Ionic event`);\r\n    }\r\n\r\n    // Check if user already has a high priority quest in this category\r\n    this.questService.getQuests(this.userId).pipe(\r\n      take(1),\r\n      map(quests => {\r\n        return quests.some(q =>\r\n          q.category === this.newQuest.category &&\r\n          q.priority === 'high' &&\r\n          q.active\r\n        );\r\n      })\r\n    ).subscribe({\r\n      next: (hasHighPriority) => {\r\n        this.hasHighPriorityQuest = hasHighPriority;\r\n\r\n        // If user already has a high priority quest, set this one to basic\r\n        if (hasHighPriority) {\r\n          this.newQuest.priority = 'basic';\r\n        }\r\n\r\n        console.log(`TodayPage: Category ${this.newQuest.category} has high priority quest: ${hasHighPriority}`);\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Check if all quests are completed for today and show celebration if enabled\r\n   */\r\n  checkAllQuestsCompleted(quests: QuestDisplay[]) {\r\n    // Only check for today's date\r\n    const today = new Date();\r\n    today.setHours(0, 0, 0, 0);\r\n    const selectedDate = new Date(this.selectedDate);\r\n    selectedDate.setHours(0, 0, 0, 0);\r\n    const isTodaySelected = selectedDate.getTime() === today.getTime();\r\n    const todayStr = this.formatDate(today);\r\n\r\n    if (!isTodaySelected || !this.currentUser) {\r\n      return;\r\n    }\r\n\r\n    // Check if celebration has already been shown for today\r\n    const celebrationShown = localStorage.getItem(`celebration_shown_${todayStr}`);\r\n    if (celebrationShown) {\r\n      console.log('TodayPage: Celebration already shown for today:', todayStr);\r\n      return;\r\n    }\r\n\r\n    // Check if all quests are completed\r\n    const allQuestsCompleted = quests.length > 0 && quests.every(quest => quest.completed);\r\n\r\n    // Check if side quest is completed (if enabled)\r\n    const sideQuestCompleted = !this.showSidequests || !this.dailyQuest || this.dailyQuest.completed;\r\n\r\n    // Show celebration if all quests and side quests are completed and celebration is enabled\r\n\r\n    if (allQuestsCompleted && sideQuestCompleted && this.currentUser.show_celebration) {\r\n      // Make sure we have the latest user data\r\n      this.userService.getUserById(this.userId!).subscribe(userData => {\r\n        if (userData) {\r\n          this.currentUser = userData;\r\n        }\r\n\r\n        // Show the celebration\r\n        this.showCelebration = true;\r\n\r\n        // Save today's date to localStorage\r\n        localStorage.setItem(`celebration_shown_${todayStr}`, 'true');\r\n\r\n        // Update our tracking array\r\n        if (!this.celebrationShownDates.includes(todayStr)) {\r\n          this.celebrationShownDates.push(todayStr);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Close the celebration modal\r\n   */\r\n  closeCelebration() {\r\n    this.showCelebration = false;\r\n  }\r\n\r\n\r\n\r\n  // Helper methods\r\n  formatDate(date: Date): string {\r\n    const year = date.getFullYear();\r\n    const month = String(date.getMonth() + 1).padStart(2, '0');\r\n    const day = String(date.getDate()).padStart(2, '0');\r\n    return `${year}-${month}-${day}`;\r\n  }\r\n\r\n  isSameDay(date1: Date, date2: Date): boolean {\r\n    return date1.getFullYear() === date2.getFullYear() &&\r\n      date1.getMonth() === date2.getMonth() &&\r\n      date1.getDate() === date2.getDate();\r\n  }\r\n\r\n  getToday(): Date {\r\n    const today = new Date();\r\n    today.setHours(0, 0, 0, 0);\r\n    return today;\r\n  }\r\n\r\n  // Convert Django day index (0=Monday, 6=Sunday) to short day name\r\n  private getDayNameShort(djangoDayIndex: number): string {\r\n    // Map Django day index to day name\r\n    const dayMap = ['Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa', 'Su'];\r\n    return dayMap[djangoDayIndex];\r\n  }\r\n\r\n  // Convert Django day index (0=Monday, 6=Sunday) to full day name\r\n  private getDayNameFull(djangoDayIndex: number): string {\r\n    // Map Django day index to full day name\r\n    const dayMap = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];\r\n    return dayMap[djangoDayIndex];\r\n  }\r\n\r\n  private getEmptyQuest(): Partial<Quest> {\r\n    return {\r\n      name: '',\r\n      description: '',\r\n      quest_type: 'build' as QuestType,\r\n      goal_value: 1,\r\n      goal_unit: 'count' as QuestGoalUnit,\r\n      goal_period: 'day' as QuestPeriod,\r\n      priority: 'basic' as QuestPriority, // Default to basic priority\r\n      category: '' as QuestCategory,\r\n      emoji: '🎯'\r\n    };\r\n  }\r\n\r\n  // Helper methods for the template\r\n  // Note: Progress slider background is now handled via CSS variables\r\n}\r\n", "<ion-content class=\"ion-padding\" [fullscreen]=\"true\">\r\n  <div class=\"background-container\">\r\n    <div class=\"gradient-bg\"></div>\r\n    <div class=\"celestial-body\"></div>\r\n  </div>\r\n  <ion-header class=\"ion-no-border\">\r\n    <ion-toolbar>\r\n      <app-header [headerText]=\"headerText\"></app-header>\r\n      <ion-row class=\"week-row ion-padding-top\">\r\n        <div class=\"day-container\" *ngFor=\"let date of weekDates; let i = index\">\r\n          <ion-text class=\"day-name\" [class.active]=\"date.is_today\"\r\n            [class.selected]=\"date.is_selected && !date.is_today\" [class.unselected]=\"!date.is_selected\">\r\n            {{ ['M', 'T', 'W', 'T', 'F', 'S', 'S'][i] }}\r\n          </ion-text>\r\n          <div class=\"date\" [class.selected]=\"date.is_selected\" [class.disabled]=\"date.is_future\"\r\n            (click)=\"!date.is_future && selectDate(date)\">\r\n            <svg class=\"date-progress\" viewBox=\"0 0 36 36\">\r\n              <circle cx=\"18\" cy=\"18\" r=\"13\" stroke-dasharray=\"81.68, 81.68\" class=\"background-circle\">\r\n              </circle>\r\n\r\n              <circle cx=\"18\" cy=\"18\" r=\"13\" *ngIf=\"!date.is_future\"\r\n                [attr.stroke-dasharray]=\"(date.completion_percentage * 81.68 / 100) + ', 81.68'\"\r\n                [attr.data-date]=\"date.date\" class=\"progress-circle\" [class.low]=\"date.completion_percentage < 50\">\r\n              </circle>\r\n            </svg>\r\n          </div>\r\n        </div>\r\n      </ion-row>\r\n    </ion-toolbar>\r\n  </ion-header>\r\n  <ion-grid>\r\n    <ion-row class=\"ion-justify-content-center\">\r\n      <div class=\"heartbeat-circle gradient-text\"></div>\r\n    </ion-row>\r\n    <ion-row class=\"add-quest\">\r\n      <ion-col>\r\n        <h2>Quests</h2>\r\n      </ion-col>\r\n      <ion-col>\r\n        <ion-button fill=\"clear\" id=\"add-quest-btn\" class=\"add-quest-btn\" (click)=\"openAddQuestModal($event)\">\r\n          + Add Quest\r\n        </ion-button>\r\n      </ion-col>\r\n    </ion-row>\r\n    <ion-row class=\"quests\">\r\n      <ion-col>\r\n        <ion-card *ngif=\"quests.length === 0\" class=\"ion-text-center no-quest-card\">\r\n          <ion-card-header>\r\n            <h2>No quests found..</h2>\r\n          </ion-card-header>\r\n          <ion-card-content>\r\n            <ion-row>\r\n              <ion-col size=\"8\">\r\n                <ion-text>No quests found. Try adding a quest ;)</ion-text>\r\n              </ion-col>\r\n              <ion-col size=\"4\">\r\n                <ion-icon name=\"warning\"></ion-icon>\r\n              </ion-col>\r\n            </ion-row>\r\n          </ion-card-content>\r\n        </ion-card>\r\n        <ion-card *ngFor=\"let quest of quests\" class=\"quest-item ion-margin-bottom\" [class.completed]=\"quest.completed\"\r\n          [attr.data-quest-id]=\"quest.id\" [attr.data-regular-quest]=\"true\" (click)=\"toggleQuest(quest)\">\r\n          <ion-row>\r\n            <ion-col size=\"2\">\r\n              <div class=\"quest-icon\">\r\n                {{ quest.emoji }}\r\n              </div>\r\n            </ion-col>\r\n            <ion-col size=\"8\" class=\"quest-info\">\r\n              <h3>{{ quest.name }}</h3>\r\n              <ion-text>{{ quest.description }}</ion-text>\r\n              <div class=\"progress-container\">\r\n                <div class=\"progress-time\"\r\n                  *ngIf=\"quest.goal_unit === 'time' || quest.goal_unit === 'min' || quest.goal_unit === 'hr' || quest.goal_unit === 'sec'\">\r\n                  <ion-range min=\"0\" [max]=\"quest.goal_value\" [(ngModel)]=\"quest.value_achieved\" class=\"progress-slider\"\r\n                    [attr.data-quest-id]=\"quest.id\" [attr.data-quest-type]=\"quest.quest_type\" [step]=\"1\" snaps=\"true\"\r\n                    ticks=\"false\" snaps-per-step=\"true\" (ionChange)=\"updateQuestProgress(quest, $event)\"\r\n                    (ionInput)=\"$event.target && updateSliderBackground($event.target)\"\r\n                    style=\"--progress-value: {{quest.value_achieved / quest.goal_value * 100}}%\">\r\n                  </ion-range>\r\n                  <ion-text class=\"progress-text\">\r\n                    {{ quest.value_achieved }}{{ quest.goal_unit === 'min' ? 'm' : quest.goal_unit === 'hr' ? 'h' :\r\n                    's'\r\n                    }}/{{ quest.goal_value }}{{ quest.goal_unit === 'min' ? 'm' : quest.goal_unit === 'hr' ? 'h' : 's'\r\n                    }}\r\n                  </ion-text>\r\n                </div>\r\n                <div class=\"progress\"\r\n                  *ngIf=\"quest.goal_unit !== 'time' && quest.goal_unit !== 'min' && quest.goal_unit !== 'hr' && quest.goal_unit !== 'sec'\">\r\n                  <ion-range min=\"0\" [max]=\"quest.goal_value\" [(ngModel)]=\"quest.value_achieved\" class=\"progress-slider\"\r\n                    [attr.data-quest-id]=\"quest.id\" [attr.data-quest-type]=\"quest.quest_type\" [step]=\"1\" snaps=\"true\"\r\n                    ticks=\"false\" snaps-per-step=\"true\" (ionChange)=\"updateQuestProgress(quest, $event)\"\r\n                    (ionInput)=\"$event.target && updateSliderBackground($event.target)\"\r\n                    style=\"--progress-value: {{quest.value_achieved / quest.goal_value * 100}}%\">\r\n                  </ion-range>\r\n                  <ion-text class=\"progress-text\">\r\n                    {{ quest.value_achieved }}/{{ quest.goal_value }}\r\n                    <ng-container *ngIf=\"quest.goal_unit !== 'count'\">\r\n                      {{ quest.goal_unit }}\r\n                    </ng-container>\r\n                  </ion-text>\r\n                </div>\r\n              </div>\r\n            </ion-col>\r\n            <ion-col size=\"2\">\r\n              <ion-text class=\"quest-streak\" *ngIf=\"isSameDay(selectedDate, getToday())\">\r\n                🔥{{ quest.streak }}d\r\n              </ion-text>\r\n            </ion-col>\r\n          </ion-row>\r\n        </ion-card>\r\n      </ion-col>\r\n    </ion-row>\r\n    <ion-row class=\"ion-padding-top\">\r\n      <ion-col>\r\n        <h2>Daily Side Quest</h2>\r\n      </ion-col>\r\n    </ion-row>\r\n    <ion-row class=\"quests\">\r\n      <ion-col>\r\n        <ion-card *ngIf=\"dailyQuest && dailyQuest.current_quest\" class=\"quest-item\"\r\n          [class.completed]=\"dailyQuest.completed\" [attr.data-quest-id]=\"dailyQuest.id\" [attr.data-side-quest]=\"true\"\r\n          (click)=\"toggleSideQuest(dailyQuest)\">\r\n          <ion-row>\r\n            <ion-col size=\"2\">\r\n              <div class=\"quest-icon\">\r\n                {{ dailyQuest.emoji }}\r\n              </div>\r\n            </ion-col>\r\n            <ion-col class=\"quest-info\" size=\"8\">\r\n              <h3>{{ dailyQuest.current_quest.name }}</h3>\r\n              <ion-text class=\"quest-description\">{{ dailyQuest.current_quest.description }}</ion-text>\r\n              <!-- <div class=\"progress-text\">\r\n              {{ dailyQuest.value_achieved }}/{{ dailyQuest.current_quest.goal_value }}\r\n              <ng-container *ngIf=\"dailyQuest.current_quest.goal_unit !== 'count'\">\r\n                {{ dailyQuest.current_quest.goal_unit }}\r\n              </ng-container>\r\n            </div> -->\r\n            </ion-col>\r\n            <ion-col size=\"2\">\r\n              <div class=\"quest-streak\" *ngIf=\"isSameDay(selectedDate, getToday())\">\r\n                🔥{{ dailyQuest.streak }}d\r\n              </div>\r\n            </ion-col>\r\n          </ion-row>\r\n        </ion-card>\r\n        <ion-card class=\"quest-item\" *ngIf=\"!dailyQuest\">\r\n          <ion-card-header>\r\n            <ion-card-title>\r\n              Daily Side Quest\r\n            </ion-card-title>\r\n          </ion-card-header>\r\n          <ion-card-content>\r\n            <ion-text>\r\n              No daily side quests are currently available. Please check back later or contact an administrator.\r\n            </ion-text>\r\n          </ion-card-content>\r\n        </ion-card>\r\n      </ion-col>\r\n    </ion-row>\r\n  </ion-grid>\r\n\r\n  <!-- Add Quest Modal -->\r\n  <ion-modal [isOpen]=\"true\" class=\"add-quest-modal\" (ionModalDidDismiss)=\"closeAddQuestModal()\"\r\n    [backdropDismiss]=\"true\" [breakpoints]=\"[0, 0.25, 0.5, 0.75, 1]\" [initialBreakpoint]=\"0.75\">\r\n    <ng-template>\r\n      <ion-header class=\"modal-header\">\r\n        <ion-toolbar>\r\n          <ion-title class=\"modal-title\">\r\n            <div class=\"title-content\">\r\n              <div class=\"title-icon\">✨</div>\r\n              <span>Create New Quest</span>\r\n            </div>\r\n          </ion-title>\r\n          <ion-buttons slot=\"end\">\r\n            <ion-button fill=\"clear\" class=\"close-button\" (click)=\"closeAddQuestModal()\">\r\n              <ion-icon name=\"close\" size=\"large\"></ion-icon>\r\n            </ion-button>\r\n          </ion-buttons>\r\n        </ion-toolbar>\r\n      </ion-header>\r\n\r\n      <ion-content class=\"modal-content\">\r\n        <div class=\"modal-body\">\r\n          <form (ngSubmit)=\"createQuest()\" #questForm=\"ngForm\" class=\"quest-form\">\r\n\r\n            <!-- Quest Identity Section -->\r\n            <div class=\"form-section\">\r\n              <div class=\"section-header\">\r\n                <h3>Quest Identity</h3>\r\n                <div class=\"section-divider\"></div>\r\n              </div>\r\n\r\n              <div class=\"quest-identity-row\">\r\n                <div class=\"emoji-container\">\r\n                  <ion-input\r\n                    type=\"text\"\r\n                    id=\"emoji\"\r\n                    name=\"emoji\"\r\n                    [(ngModel)]=\"newQuest.emoji\"\r\n                    value=\"🎯\"\r\n                    appEmojiInput\r\n                    class=\"emoji-input\"\r\n                    placeholder=\"🎯\"\r\n                    required>\r\n                  </ion-input>\r\n                  <label class=\"emoji-label\">Icon</label>\r\n                </div>\r\n\r\n                <div class=\"name-container\">\r\n                  <ion-input\r\n                    type=\"text\"\r\n                    id=\"name\"\r\n                    name=\"name\"\r\n                    [(ngModel)]=\"newQuest.name\"\r\n                    placeholder=\" \"\r\n                    class=\"name-input\"\r\n                    required>\r\n                  </ion-input>\r\n                  <label class=\"floating-label\">Quest Name</label>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"input-group\">\r\n                <ion-textarea\r\n                  id=\"description\"\r\n                  name=\"description\"\r\n                  [(ngModel)]=\"newQuest.description\"\r\n                  placeholder=\" \"\r\n                  class=\"description-input\"\r\n                  rows=\"3\">\r\n                </ion-textarea>\r\n                <label class=\"floating-label\">Description (Optional)</label>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Quest Configuration Section -->\r\n            <div class=\"form-section\">\r\n              <div class=\"section-header\">\r\n                <h3>Configuration</h3>\r\n                <div class=\"section-divider\"></div>\r\n              </div>\r\n\r\n              <div class=\"input-group\">\r\n                <ion-select\r\n                  id=\"quest_type\"\r\n                  name=\"quest_type\"\r\n                  [(ngModel)]=\"newQuest.quest_type\"\r\n                  interface=\"popover\"\r\n                  class=\"modern-select\"\r\n                  placeholder=\" \"\r\n                  required>\r\n                  <ion-select-option value=\"build\">🏗️ Build Habit</ion-select-option>\r\n                  <ion-select-option value=\"quit\">🚫 Quit Habit</ion-select-option>\r\n                </ion-select>\r\n                <label class=\"floating-label\">Quest Type</label>\r\n              </div>\r\n\r\n              <div class=\"input-group\">\r\n                <ion-select\r\n                  id=\"category\"\r\n                  name=\"category\"\r\n                  [(ngModel)]=\"newQuest.category\"\r\n                  required\r\n                  (ionChange)=\"checkCategoryPriority($event)\"\r\n                  interface=\"popover\"\r\n                  class=\"modern-select\"\r\n                  placeholder=\" \">\r\n                  <ion-select-option value=\"\">Select a category</ion-select-option>\r\n                  <ion-select-option value=\"strength\">💪 Strength</ion-select-option>\r\n                  <ion-select-option value=\"money\">💰 Money</ion-select-option>\r\n                  <ion-select-option value=\"health\">🏥 Health</ion-select-option>\r\n                  <ion-select-option value=\"knowledge\">📚 Knowledge</ion-select-option>\r\n                </ion-select>\r\n                <label class=\"floating-label\">Category</label>\r\n              </div>\r\n\r\n              <div class=\"input-group\" *ngIf=\"newQuest.category\">\r\n                <ion-select\r\n                  id=\"priority\"\r\n                  name=\"priority\"\r\n                  [(ngModel)]=\"newQuest.priority\"\r\n                  [disabled]=\"hasHighPriorityQuest\"\r\n                  interface=\"popover\"\r\n                  class=\"modern-select\"\r\n                  placeholder=\" \">\r\n                  <ion-select-option value=\"basic\">⭐ Basic</ion-select-option>\r\n                  <ion-select-option value=\"high\">🔥 High Priority</ion-select-option>\r\n                </ion-select>\r\n                <label class=\"floating-label\">Priority Level</label>\r\n                <div class=\"priority-warning\" *ngIf=\"hasHighPriorityQuest\">\r\n                  <ion-icon name=\"warning\" color=\"warning\"></ion-icon>\r\n                  <span>You already have a high priority quest in this category</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Goal & Tracking Section -->\r\n            <div class=\"form-section\">\r\n              <div class=\"section-header\">\r\n                <h3>Goal & Tracking</h3>\r\n                <div class=\"section-divider\"></div>\r\n              </div>\r\n\r\n              <div class=\"goal-row\">\r\n                <div class=\"goal-value-container\">\r\n                  <ion-input\r\n                    type=\"number\"\r\n                    id=\"goal_value\"\r\n                    name=\"goal_value\"\r\n                    [(ngModel)]=\"newQuest.goal_value\"\r\n                    value=\"1\"\r\n                    min=\"1\"\r\n                    class=\"goal-input\"\r\n                    placeholder=\" \"\r\n                    required>\r\n                  </ion-input>\r\n                  <label class=\"floating-label\">Target</label>\r\n                </div>\r\n\r\n                <div class=\"goal-unit-container\">\r\n                  <ion-select\r\n                    id=\"goal_unit\"\r\n                    name=\"goal_unit\"\r\n                    [(ngModel)]=\"newQuest.goal_unit\"\r\n                    interface=\"popover\"\r\n                    class=\"modern-select unit-select\"\r\n                    placeholder=\" \"\r\n                    required>\r\n                    <ion-select-option value=\"count\">times</ion-select-option>\r\n                    <ion-select-option value=\"steps\">steps</ion-select-option>\r\n                    <ion-select-option value=\"m\">meters</ion-select-option>\r\n                    <ion-select-option value=\"km\">kilometers</ion-select-option>\r\n                    <ion-select-option value=\"sec\">seconds</ion-select-option>\r\n                    <ion-select-option value=\"min\">minutes</ion-select-option>\r\n                    <ion-select-option value=\"hr\">hours</ion-select-option>\r\n                    <ion-select-option value=\"Cal\">calories</ion-select-option>\r\n                    <ion-select-option value=\"g\">grams</ion-select-option>\r\n                    <ion-select-option value=\"mg\">milligrams</ion-select-option>\r\n                    <ion-select-option value=\"l\">liters</ion-select-option>\r\n                    <ion-select-option value=\"drink\">drinks</ion-select-option>\r\n                    <ion-select-option value=\"pages\">pages</ion-select-option>\r\n                    <ion-select-option value=\"books\">books</ion-select-option>\r\n                    <ion-select-option value=\"%\">percent</ion-select-option>\r\n                    <ion-select-option value=\"€\">euros</ion-select-option>\r\n                    <ion-select-option value=\"$\">dollars</ion-select-option>\r\n                    <ion-select-option value=\"£\">pounds</ion-select-option>\r\n                  </ion-select>\r\n                  <label class=\"floating-label\">Unit</label>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"input-group\">\r\n                <ion-select\r\n                  id=\"goal_period\"\r\n                  name=\"goal_period\"\r\n                  [(ngModel)]=\"newQuest.goal_period\"\r\n                  (ionChange)=\"updatePeriodDisplay()\"\r\n                  interface=\"popover\"\r\n                  class=\"modern-select\"\r\n                  placeholder=\" \"\r\n                  required>\r\n                  <ion-select-option value=\"day\">📅 Every Day</ion-select-option>\r\n                  <ion-select-option value=\"week\">📆 Specific Days of Week</ion-select-option>\r\n                  <ion-select-option value=\"month\">🗓️ Specific Days of Month</ion-select-option>\r\n                </ion-select>\r\n                <label class=\"floating-label\">Frequency</label>\r\n              </div>\r\n\r\n              <div class=\"schedule-section\" *ngIf=\"newQuest.goal_period === 'week'\">\r\n                <h4>Select Days of Week</h4>\r\n                <div class=\"days-grid\">\r\n                  <div class=\"day-item\" *ngFor=\"let day of weekDays\">\r\n                    <ion-checkbox\r\n                      [id]=\"'day-' + day.value.toLowerCase()\"\r\n                      name=\"days_of_week\"\r\n                      [value]=\"day.value\"\r\n                      (ionChange)=\"updateDaysOfWeek($event, day.value)\"\r\n                      class=\"modern-checkbox\">\r\n                    </ion-checkbox>\r\n                    <label [for]=\"'day-' + day.value.toLowerCase()\" class=\"day-label\">\r\n                      {{ day.label }}\r\n                    </label>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"schedule-section\" *ngIf=\"newQuest.goal_period === 'month'\">\r\n                <h4>Select Days of Month</h4>\r\n                <div class=\"month-days-grid\">\r\n                  <div class=\"month-day-item\" *ngFor=\"let day of monthDays\">\r\n                    <ion-checkbox\r\n                      [id]=\"'month-day-' + day\"\r\n                      name=\"days_of_month\"\r\n                      [value]=\"day\"\r\n                      (ionChange)=\"updateDaysOfMonth($event, day)\"\r\n                      class=\"modern-checkbox\">\r\n                    </ion-checkbox>\r\n                    <label [for]=\"'month-day-' + day\" class=\"month-day-label\">\r\n                      {{ day }}\r\n                    </label>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </ion-content>\r\n\r\n      <ion-footer class=\"modal-footer\">\r\n        <ion-toolbar>\r\n          <div class=\"footer-buttons\">\r\n            <ion-button\r\n              fill=\"clear\"\r\n              class=\"cancel-btn\"\r\n              (click)=\"closeAddQuestModal()\">\r\n              Cancel\r\n            </ion-button>\r\n            <ion-button\r\n              type=\"submit\"\r\n              class=\"create-quest-btn\"\r\n              [disabled]=\"!questForm?.valid\"\r\n              (click)=\"createQuest()\">\r\n              <ion-icon name=\"add\" slot=\"start\"></ion-icon>\r\n              Create Quest\r\n            </ion-button>\r\n          </div>\r\n        </ion-toolbar>\r\n      </ion-footer>\r\n    </ng-template>\r\n  </ion-modal>\r\n\r\n  <app-celebration *ngIf=\"showCelebration\" [user]=\"currentUser\" [date]=\"formatDate(selectedDate)\"\r\n    (close)=\"closeCelebration()\">\r\n  </app-celebration>\r\n</ion-content>\r\n<app-navigation></app-navigation>"], "mappings": ";;AAAA,SAAuCA,MAAM,QAAQ,eAAe;AACpE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,gBAAgB,QAAQ,kCAAkC;AACnE,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,KAAK,QAA4F,0BAA0B;AAEpI,SAAqBC,YAAY,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,EAAE,EAAEC,SAAS,EAAEC,IAAI,EAAEC,cAAc,QAAQ,MAAM;AACnG,SAASC,mBAAmB,QAAQ,kDAAkD;AACtF,SAASC,oBAAoB,QAAQ,oDAAoD;AACzF,SAASC,cAAc,EAAEC,MAAM,QAAQ,iBAAiB;AACxD,SAASC,kBAAkB,QAAQ,oCAAoC;AACvE,SAASC,mBAAmB,QAAQ,wCAAwC;AAC5E,SAASC,uBAAuB,QAAQ,kCAAkC;AAC1E,SAASC,eAAe,QAAQ,4CAA4C;;;;;;;;;;ICG9DC,EAAA,CAAAC,SAAA,iBAGS;;;;IAD8CD,EAAA,CAAAE,WAAA,QAAAC,OAAA,CAAAC,qBAAA,MAA6C;;;;;;;IAZxGJ,EADF,CAAAK,cAAA,cAAyE,mBAEwB;IAC7FL,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAO,YAAA,EAAW;IACXP,EAAA,CAAAK,cAAA,cACgD;IAA9CL,EAAA,CAAAQ,UAAA,mBAAAC,8CAAA;MAAA,MAAAN,OAAA,GAAAH,EAAA,CAAAU,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,EAAAZ,OAAA,CAAAa,SAAA,IAA4BH,MAAA,CAAAI,UAAA,CAAAd,OAAA,CAAgB;IAAA,EAAC;;IAC7CH,EAAA,CAAAK,cAAA,cAA+C;IAC7CL,EAAA,CAAAC,SAAA,iBACS;IAETD,EAAA,CAAAkB,UAAA,IAAAC,sCAAA,qBAEqG;IAI3GnB,EAFI,CAAAO,YAAA,EAAM,EACF,EACF;;;;;IAhBuBP,EAAA,CAAAoB,SAAA,EAA8B;IACDpB,EAD7B,CAAAE,WAAA,WAAAC,OAAA,CAAAkB,QAAA,CAA8B,aAAAlB,OAAA,CAAAmB,WAAA,KAAAnB,OAAA,CAAAkB,QAAA,CACF,gBAAAlB,OAAA,CAAAmB,WAAA,CAAuC;IAC5FtB,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAuB,kBAAA,MAAAvB,EAAA,CAAAwB,eAAA,KAAAC,GAAA,EAAAC,IAAA,OACF;IACkB1B,EAAA,CAAAoB,SAAA,EAAmC;IAACpB,EAApC,CAAAE,WAAA,aAAAC,OAAA,CAAAmB,WAAA,CAAmC,aAAAnB,OAAA,CAAAa,SAAA,CAAkC;IAMnDhB,EAAA,CAAAoB,SAAA,GAAqB;IAArBpB,EAAA,CAAA2B,UAAA,UAAAxB,OAAA,CAAAa,SAAA,CAAqB;;;;;IA4BvDhB,EAFJ,CAAAK,cAAA,mBAA4E,sBACzD,SACX;IAAAL,EAAA,CAAAM,MAAA,wBAAiB;IACvBN,EADuB,CAAAO,YAAA,EAAK,EACV;IAIZP,EAHN,CAAAK,cAAA,uBAAkB,cACP,kBACW,eACN;IAAAL,EAAA,CAAAM,MAAA,6CAAsC;IAClDN,EADkD,CAAAO,YAAA,EAAW,EACnD;IACVP,EAAA,CAAAK,cAAA,kBAAkB;IAChBL,EAAA,CAAAC,SAAA,oBAAoC;IAI5CD,EAHM,CAAAO,YAAA,EAAU,EACF,EACO,EACV;;;;;;IAeDP,EAFF,CAAAK,cAAA,cAC2H,oBAK1C;IAJnCL,EAAA,CAAA4B,gBAAA,2BAAAC,yEAAAC,MAAA;MAAA9B,EAAA,CAAAU,aAAA,CAAAqB,GAAA;MAAA,MAAAC,QAAA,GAAAhC,EAAA,CAAAc,aAAA,GAAAF,SAAA;MAAAZ,EAAA,CAAAiC,kBAAA,CAAAD,QAAA,CAAAE,cAAA,EAAAJ,MAAA,MAAAE,QAAA,CAAAE,cAAA,GAAAJ,MAAA;MAAA,OAAA9B,EAAA,CAAAe,WAAA,CAAAe,MAAA;IAAA,EAAkC;IAG5E9B,EADoC,CAAAQ,UAAA,uBAAA2B,qEAAAL,MAAA;MAAA9B,EAAA,CAAAU,aAAA,CAAAqB,GAAA;MAAA,MAAAC,QAAA,GAAAhC,EAAA,CAAAc,aAAA,GAAAF,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAaF,MAAA,CAAAuB,mBAAA,CAAAJ,QAAA,EAAAF,MAAA,CAAkC;IAAA,EAAC,sBAAAO,oEAAAP,MAAA;MAAA9B,EAAA,CAAAU,aAAA,CAAAqB,GAAA;MAAA,MAAAlB,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAAe,MAAA,CAAAQ,MAAA,IACvDzB,MAAA,CAAA0B,sBAAA,CAAAT,MAAA,CAAAQ,MAAA,CAAqC;IAAA,EAAC;IAErEtC,EAAA,CAAAO,YAAA,EAAY;IACZP,EAAA,CAAAK,cAAA,mBAAgC;IAC9BL,EAAA,CAAAM,MAAA,GAIF;IACFN,EADE,CAAAO,YAAA,EAAW,EACP;;;;IARFP,EAAA,CAAAoB,SAAA,EAA4E;IAA5EpB,EAAA,CAAAwC,sBAAA,uBAAAR,QAAA,CAAAE,cAAA,GAAAF,QAAA,CAAAS,UAAA,YAA4E;IAJ3DzC,EAAA,CAAA2B,UAAA,QAAAK,QAAA,CAAAS,UAAA,CAAwB;IAACzC,EAAA,CAAA0C,gBAAA,YAAAV,QAAA,CAAAE,cAAA,CAAkC;IACFlC,EAAA,CAAA2B,UAAA,WAAU;;IAMpF3B,EAAA,CAAAoB,SAAA,GAIF;IAJEpB,EAAA,CAAA2C,kBAAA,MAAAX,QAAA,CAAAE,cAAA,MAAAF,QAAA,CAAAY,SAAA,mBAAAZ,QAAA,CAAAY,SAAA,4BAAAZ,QAAA,CAAAS,UAAA,MAAAT,QAAA,CAAAY,SAAA,mBAAAZ,QAAA,CAAAY,SAAA,2BAIF;;;;;IAYE5C,EAAA,CAAA6C,uBAAA,GAAkD;IAChD7C,EAAA,CAAAM,MAAA,GACF;;;;;IADEN,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAuB,kBAAA,MAAAS,QAAA,CAAAY,SAAA,MACF;;;;;;IAVF5C,EAFF,CAAAK,cAAA,cAC2H,oBAK1C;IAJnCL,EAAA,CAAA4B,gBAAA,2BAAAkB,yEAAAhB,MAAA;MAAA9B,EAAA,CAAAU,aAAA,CAAAqC,GAAA;MAAA,MAAAf,QAAA,GAAAhC,EAAA,CAAAc,aAAA,GAAAF,SAAA;MAAAZ,EAAA,CAAAiC,kBAAA,CAAAD,QAAA,CAAAE,cAAA,EAAAJ,MAAA,MAAAE,QAAA,CAAAE,cAAA,GAAAJ,MAAA;MAAA,OAAA9B,EAAA,CAAAe,WAAA,CAAAe,MAAA;IAAA,EAAkC;IAG5E9B,EADoC,CAAAQ,UAAA,uBAAAwC,qEAAAlB,MAAA;MAAA9B,EAAA,CAAAU,aAAA,CAAAqC,GAAA;MAAA,MAAAf,QAAA,GAAAhC,EAAA,CAAAc,aAAA,GAAAF,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAaF,MAAA,CAAAuB,mBAAA,CAAAJ,QAAA,EAAAF,MAAA,CAAkC;IAAA,EAAC,sBAAAmB,oEAAAnB,MAAA;MAAA9B,EAAA,CAAAU,aAAA,CAAAqC,GAAA;MAAA,MAAAlC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAAe,MAAA,CAAAQ,MAAA,IACvDzB,MAAA,CAAA0B,sBAAA,CAAAT,MAAA,CAAAQ,MAAA,CAAqC;IAAA,EAAC;IAErEtC,EAAA,CAAAO,YAAA,EAAY;IACZP,EAAA,CAAAK,cAAA,mBAAgC;IAC9BL,EAAA,CAAAM,MAAA,GACA;IAAAN,EAAA,CAAAkB,UAAA,IAAAgC,oDAAA,2BAAkD;IAItDlD,EADE,CAAAO,YAAA,EAAW,EACP;;;;IARFP,EAAA,CAAAoB,SAAA,EAA4E;IAA5EpB,EAAA,CAAAwC,sBAAA,uBAAAR,QAAA,CAAAE,cAAA,GAAAF,QAAA,CAAAS,UAAA,YAA4E;IAJ3DzC,EAAA,CAAA2B,UAAA,QAAAK,QAAA,CAAAS,UAAA,CAAwB;IAACzC,EAAA,CAAA0C,gBAAA,YAAAV,QAAA,CAAAE,cAAA,CAAkC;IACFlC,EAAA,CAAA2B,UAAA,WAAU;;IAMpF3B,EAAA,CAAAoB,SAAA,GACA;IADApB,EAAA,CAAAmD,kBAAA,MAAAnB,QAAA,CAAAE,cAAA,OAAAF,QAAA,CAAAS,UAAA,MACA;IAAezC,EAAA,CAAAoB,SAAA,EAAiC;IAAjCpB,EAAA,CAAA2B,UAAA,SAAAK,QAAA,CAAAY,SAAA,aAAiC;;;;;IAQtD5C,EAAA,CAAAK,cAAA,mBAA2E;IACzEL,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAO,YAAA,EAAW;;;;IADTP,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAuB,kBAAA,kBAAAS,QAAA,CAAAoB,MAAA,OACF;;;;;;IA/CNpD,EAAA,CAAAK,cAAA,mBACgG;IAA7BL,EAAA,CAAAQ,UAAA,mBAAA6C,yDAAA;MAAA,MAAArB,QAAA,GAAAhC,EAAA,CAAAU,aAAA,CAAA4C,GAAA,EAAA1C,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAA0C,WAAA,CAAAvB,QAAA,CAAkB;IAAA,EAAC;IAGzFhC,EAFJ,CAAAK,cAAA,cAAS,kBACW,cACQ;IACtBL,EAAA,CAAAM,MAAA,GACF;IACFN,EADE,CAAAO,YAAA,EAAM,EACE;IAERP,EADF,CAAAK,cAAA,kBAAqC,SAC/B;IAAAL,EAAA,CAAAM,MAAA,GAAgB;IAAAN,EAAA,CAAAO,YAAA,EAAK;IACzBP,EAAA,CAAAK,cAAA,eAAU;IAAAL,EAAA,CAAAM,MAAA,GAAuB;IAAAN,EAAA,CAAAO,YAAA,EAAW;IAC5CP,EAAA,CAAAK,cAAA,eAAgC;IAgB9BL,EAfA,CAAAkB,UAAA,KAAAsC,qCAAA,mBAC2H,KAAAC,qCAAA,mBAeA;IAe/HzD,EADE,CAAAO,YAAA,EAAM,EACE;IACVP,EAAA,CAAAK,cAAA,mBAAkB;IAChBL,EAAA,CAAAkB,UAAA,KAAAwC,0CAAA,uBAA2E;IAKjF1D,EAFI,CAAAO,YAAA,EAAU,EACF,EACD;;;;;IAlDiEP,EAAA,CAAAE,WAAA,cAAA8B,QAAA,CAAA2B,SAAA,CAAmC;;IAKvG3D,EAAA,CAAAoB,SAAA,GACF;IADEpB,EAAA,CAAAuB,kBAAA,MAAAS,QAAA,CAAA4B,KAAA,MACF;IAGI5D,EAAA,CAAAoB,SAAA,GAAgB;IAAhBpB,EAAA,CAAA6D,iBAAA,CAAA7B,QAAA,CAAA8B,IAAA,CAAgB;IACV9D,EAAA,CAAAoB,SAAA,GAAuB;IAAvBpB,EAAA,CAAA6D,iBAAA,CAAA7B,QAAA,CAAA+B,WAAA,CAAuB;IAG5B/D,EAAA,CAAAoB,SAAA,GAAsH;IAAtHpB,EAAA,CAAA2B,UAAA,SAAAK,QAAA,CAAAY,SAAA,eAAAZ,QAAA,CAAAY,SAAA,cAAAZ,QAAA,CAAAY,SAAA,aAAAZ,QAAA,CAAAY,SAAA,WAAsH;IAetH5C,EAAA,CAAAoB,SAAA,EAAsH;IAAtHpB,EAAA,CAAA2B,UAAA,SAAAK,QAAA,CAAAY,SAAA,eAAAZ,QAAA,CAAAY,SAAA,cAAAZ,QAAA,CAAAY,SAAA,aAAAZ,QAAA,CAAAY,SAAA,WAAsH;IAiB3F5C,EAAA,CAAAoB,SAAA,GAAyC;IAAzCpB,EAAA,CAAA2B,UAAA,SAAAd,MAAA,CAAAmD,SAAA,CAAAnD,MAAA,CAAAoD,YAAA,EAAApD,MAAA,CAAAqD,QAAA,IAAyC;;;;;IAmCzElE,EAAA,CAAAK,cAAA,cAAsE;IACpEL,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAO,YAAA,EAAM;;;;IADJP,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAuB,kBAAA,kBAAAV,MAAA,CAAAsD,UAAA,CAAAf,MAAA,OACF;;;;;;IAtBNpD,EAAA,CAAAK,cAAA,mBAEwC;IAAtCL,EAAA,CAAAQ,UAAA,mBAAA4D,yDAAA;MAAApE,EAAA,CAAAU,aAAA,CAAA2D,GAAA;MAAA,MAAAxD,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAyD,eAAA,CAAAzD,MAAA,CAAAsD,UAAA,CAA2B;IAAA,EAAC;IAGjCnE,EAFJ,CAAAK,cAAA,cAAS,kBACW,cACQ;IACtBL,EAAA,CAAAM,MAAA,GACF;IACFN,EADE,CAAAO,YAAA,EAAM,EACE;IAERP,EADF,CAAAK,cAAA,kBAAqC,SAC/B;IAAAL,EAAA,CAAAM,MAAA,GAAmC;IAAAN,EAAA,CAAAO,YAAA,EAAK;IAC5CP,EAAA,CAAAK,cAAA,mBAAoC;IAAAL,EAAA,CAAAM,MAAA,GAA0C;IAOhFN,EAPgF,CAAAO,YAAA,EAAW,EAOjF;IACVP,EAAA,CAAAK,cAAA,mBAAkB;IAChBL,EAAA,CAAAkB,UAAA,KAAAqD,qCAAA,kBAAsE;IAK5EvE,EAFI,CAAAO,YAAA,EAAU,EACF,EACD;;;;IAxBTP,EAAA,CAAAE,WAAA,cAAAW,MAAA,CAAAsD,UAAA,CAAAR,SAAA,CAAwC;;IAKlC3D,EAAA,CAAAoB,SAAA,GACF;IADEpB,EAAA,CAAAuB,kBAAA,MAAAV,MAAA,CAAAsD,UAAA,CAAAP,KAAA,MACF;IAGI5D,EAAA,CAAAoB,SAAA,GAAmC;IAAnCpB,EAAA,CAAA6D,iBAAA,CAAAhD,MAAA,CAAAsD,UAAA,CAAAK,aAAA,CAAAV,IAAA,CAAmC;IACH9D,EAAA,CAAAoB,SAAA,GAA0C;IAA1CpB,EAAA,CAAA6D,iBAAA,CAAAhD,MAAA,CAAAsD,UAAA,CAAAK,aAAA,CAAAT,WAAA,CAA0C;IASnD/D,EAAA,CAAAoB,SAAA,GAAyC;IAAzCpB,EAAA,CAAA2B,UAAA,SAAAd,MAAA,CAAAmD,SAAA,CAAAnD,MAAA,CAAAoD,YAAA,EAAApD,MAAA,CAAAqD,QAAA,IAAyC;;;;;IAQtElE,EAFJ,CAAAK,cAAA,mBAAiD,sBAC9B,qBACC;IACdL,EAAA,CAAAM,MAAA,yBACF;IACFN,EADE,CAAAO,YAAA,EAAiB,EACD;IAEhBP,EADF,CAAAK,cAAA,uBAAkB,eACN;IACRL,EAAA,CAAAM,MAAA,2GACF;IAEJN,EAFI,CAAAO,YAAA,EAAW,EACM,EACV;;;;;IAqIHP,EAAA,CAAAK,cAAA,eAA2D;IACzDL,EAAA,CAAAC,SAAA,oBAAoD;IACpDD,EAAA,CAAAK,cAAA,WAAM;IAAAL,EAAA,CAAAM,MAAA,8DAAuD;IAC/DN,EAD+D,CAAAO,YAAA,EAAO,EAChE;;;;;;IAfNP,EADF,CAAAK,cAAA,cAAmD,sBAQ/B;IAJhBL,EAAA,CAAA4B,gBAAA,2BAAA6C,6EAAA3C,MAAA;MAAA9B,EAAA,CAAAU,aAAA,CAAAgE,IAAA;MAAA,MAAA7D,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAAiC,kBAAA,CAAApB,MAAA,CAAA8D,QAAA,CAAAC,QAAA,EAAA9C,MAAA,MAAAjB,MAAA,CAAA8D,QAAA,CAAAC,QAAA,GAAA9C,MAAA;MAAA,OAAA9B,EAAA,CAAAe,WAAA,CAAAe,MAAA;IAAA,EAA+B;IAK/B9B,EAAA,CAAAK,cAAA,6BAAiC;IAAAL,EAAA,CAAAM,MAAA,mBAAO;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IAC5DP,EAAA,CAAAK,cAAA,6BAAgC;IAAAL,EAAA,CAAAM,MAAA,iCAAgB;IAClDN,EADkD,CAAAO,YAAA,EAAoB,EACzD;IACbP,EAAA,CAAAK,cAAA,gBAA8B;IAAAL,EAAA,CAAAM,MAAA,qBAAc;IAAAN,EAAA,CAAAO,YAAA,EAAQ;IACpDP,EAAA,CAAAkB,UAAA,IAAA2D,8CAAA,mBAA2D;IAI7D7E,EAAA,CAAAO,YAAA,EAAM;;;;IAbFP,EAAA,CAAAoB,SAAA,EAA+B;IAA/BpB,EAAA,CAAA0C,gBAAA,YAAA7B,MAAA,CAAA8D,QAAA,CAAAC,QAAA,CAA+B;IAC/B5E,EAAA,CAAA2B,UAAA,aAAAd,MAAA,CAAAiE,oBAAA,CAAiC;IAQJ9E,EAAA,CAAAoB,SAAA,GAA0B;IAA1BpB,EAAA,CAAA2B,UAAA,SAAAd,MAAA,CAAAiE,oBAAA,CAA0B;;;;;;IAmFrD9E,EADF,CAAAK,cAAA,eAAmD,wBAMvB;IADxBL,EAAA,CAAAQ,UAAA,uBAAAuE,kFAAAjD,MAAA;MAAA,MAAAkD,OAAA,GAAAhF,EAAA,CAAAU,aAAA,CAAAuE,IAAA,EAAArE,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAaF,MAAA,CAAAqE,gBAAA,CAAApD,MAAA,EAAAkD,OAAA,CAAAG,KAAA,CAAmC;IAAA,EAAC;IAEnDnF,EAAA,CAAAO,YAAA,EAAe;IACfP,EAAA,CAAAK,cAAA,iBAAkE;IAChEL,EAAA,CAAAM,MAAA,GACF;IACFN,EADE,CAAAO,YAAA,EAAQ,EACJ;;;;IATFP,EAAA,CAAAoB,SAAA,EAAuC;IAEvCpB,EAFA,CAAA2B,UAAA,gBAAAqD,OAAA,CAAAG,KAAA,CAAAC,WAAA,GAAuC,UAAAJ,OAAA,CAAAG,KAAA,CAEpB;IAIdnF,EAAA,CAAAoB,SAAA,EAAwC;IAAxCpB,EAAA,CAAA2B,UAAA,iBAAAqD,OAAA,CAAAG,KAAA,CAAAC,WAAA,GAAwC;IAC7CpF,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAuB,kBAAA,MAAAyD,OAAA,CAAAK,KAAA,MACF;;;;;IAZJrF,EADF,CAAAK,cAAA,eAAsE,SAChE;IAAAL,EAAA,CAAAM,MAAA,0BAAmB;IAAAN,EAAA,CAAAO,YAAA,EAAK;IAC5BP,EAAA,CAAAK,cAAA,eAAuB;IACrBL,EAAA,CAAAkB,UAAA,IAAAoE,+CAAA,mBAAmD;IAavDtF,EADE,CAAAO,YAAA,EAAM,EACF;;;;IAboCP,EAAA,CAAAoB,SAAA,GAAW;IAAXpB,EAAA,CAAA2B,UAAA,YAAAd,MAAA,CAAA0E,QAAA,CAAW;;;;;;IAmB/CvF,EADF,CAAAK,cAAA,eAA0D,wBAM9B;IADxBL,EAAA,CAAAQ,UAAA,uBAAAgF,kFAAA1D,MAAA;MAAA,MAAA2D,OAAA,GAAAzF,EAAA,CAAAU,aAAA,CAAAgF,IAAA,EAAA9E,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAaF,MAAA,CAAA8E,iBAAA,CAAA7D,MAAA,EAAA2D,OAAA,CAA8B;IAAA,EAAC;IAE9CzF,EAAA,CAAAO,YAAA,EAAe;IACfP,EAAA,CAAAK,cAAA,iBAA0D;IACxDL,EAAA,CAAAM,MAAA,GACF;IACFN,EADE,CAAAO,YAAA,EAAQ,EACJ;;;;IATFP,EAAA,CAAAoB,SAAA,EAAyB;IAEzBpB,EAFA,CAAA2B,UAAA,sBAAA8D,OAAA,CAAyB,UAAAA,OAAA,CAEZ;IAIRzF,EAAA,CAAAoB,SAAA,EAA0B;IAA1BpB,EAAA,CAAA2B,UAAA,uBAAA8D,OAAA,CAA0B;IAC/BzF,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAuB,kBAAA,MAAAkE,OAAA,MACF;;;;;IAZJzF,EADF,CAAAK,cAAA,eAAuE,SACjE;IAAAL,EAAA,CAAAM,MAAA,2BAAoB;IAAAN,EAAA,CAAAO,YAAA,EAAK;IAC7BP,EAAA,CAAAK,cAAA,eAA6B;IAC3BL,EAAA,CAAAkB,UAAA,IAAA0E,+CAAA,mBAA0D;IAa9D5F,EADE,CAAAO,YAAA,EAAM,EACF;;;;IAb0CP,EAAA,CAAAoB,SAAA,GAAY;IAAZpB,EAAA,CAAA2B,UAAA,YAAAd,MAAA,CAAAgF,SAAA,CAAY;;;;;;IA5N5D7F,EAJR,CAAAK,cAAA,qBAAiC,kBAClB,oBACoB,cACF,cACD;IAAAL,EAAA,CAAAM,MAAA,aAAC;IAAAN,EAAA,CAAAO,YAAA,EAAM;IAC/BP,EAAA,CAAAK,cAAA,WAAM;IAAAL,EAAA,CAAAM,MAAA,uBAAgB;IAE1BN,EAF0B,CAAAO,YAAA,EAAO,EACzB,EACI;IAEVP,EADF,CAAAK,cAAA,sBAAwB,qBACuD;IAA/BL,EAAA,CAAAQ,UAAA,mBAAAsF,8DAAA;MAAA9F,EAAA,CAAAU,aAAA,CAAAqF,IAAA;MAAA,MAAAlF,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAmF,kBAAA,EAAoB;IAAA,EAAC;IAC1EhG,EAAA,CAAAC,SAAA,oBAA+C;IAIvDD,EAHM,CAAAO,YAAA,EAAa,EACD,EACF,EACH;IAITP,EAFJ,CAAAK,cAAA,uBAAmC,eACT,mBACkD;IAAlEL,EAAA,CAAAQ,UAAA,sBAAAyF,4DAAA;MAAAjG,EAAA,CAAAU,aAAA,CAAAqF,IAAA;MAAA,MAAAlF,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAYF,MAAA,CAAAqF,WAAA,EAAa;IAAA,EAAC;IAK1BlG,EAFJ,CAAAK,cAAA,eAA0B,eACI,UACtB;IAAAL,EAAA,CAAAM,MAAA,sBAAc;IAAAN,EAAA,CAAAO,YAAA,EAAK;IACvBP,EAAA,CAAAC,SAAA,eAAmC;IACrCD,EAAA,CAAAO,YAAA,EAAM;IAIFP,EAFJ,CAAAK,cAAA,eAAgC,eACD,qBAUhB;IALTL,EAAA,CAAA4B,gBAAA,2BAAAuE,sEAAArE,MAAA;MAAA9B,EAAA,CAAAU,aAAA,CAAAqF,IAAA;MAAA,MAAAlF,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAAiC,kBAAA,CAAApB,MAAA,CAAA8D,QAAA,CAAAf,KAAA,EAAA9B,MAAA,MAAAjB,MAAA,CAAA8D,QAAA,CAAAf,KAAA,GAAA9B,MAAA;MAAA,OAAA9B,EAAA,CAAAe,WAAA,CAAAe,MAAA;IAAA,EAA4B;IAM9B9B,EAAA,CAAAO,YAAA,EAAY;IACZP,EAAA,CAAAK,cAAA,iBAA2B;IAAAL,EAAA,CAAAM,MAAA,YAAI;IACjCN,EADiC,CAAAO,YAAA,EAAQ,EACnC;IAGJP,EADF,CAAAK,cAAA,eAA4B,qBAQf;IAHTL,EAAA,CAAA4B,gBAAA,2BAAAwE,sEAAAtE,MAAA;MAAA9B,EAAA,CAAAU,aAAA,CAAAqF,IAAA;MAAA,MAAAlF,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAAiC,kBAAA,CAAApB,MAAA,CAAA8D,QAAA,CAAAb,IAAA,EAAAhC,MAAA,MAAAjB,MAAA,CAAA8D,QAAA,CAAAb,IAAA,GAAAhC,MAAA;MAAA,OAAA9B,EAAA,CAAAe,WAAA,CAAAe,MAAA;IAAA,EAA2B;IAI7B9B,EAAA,CAAAO,YAAA,EAAY;IACZP,EAAA,CAAAK,cAAA,iBAA8B;IAAAL,EAAA,CAAAM,MAAA,kBAAU;IAE5CN,EAF4C,CAAAO,YAAA,EAAQ,EAC5C,EACF;IAGJP,EADF,CAAAK,cAAA,eAAyB,wBAOZ;IAHTL,EAAA,CAAA4B,gBAAA,2BAAAyE,yEAAAvE,MAAA;MAAA9B,EAAA,CAAAU,aAAA,CAAAqF,IAAA;MAAA,MAAAlF,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAAiC,kBAAA,CAAApB,MAAA,CAAA8D,QAAA,CAAAZ,WAAA,EAAAjC,MAAA,MAAAjB,MAAA,CAAA8D,QAAA,CAAAZ,WAAA,GAAAjC,MAAA;MAAA,OAAA9B,EAAA,CAAAe,WAAA,CAAAe,MAAA;IAAA,EAAkC;IAIpC9B,EAAA,CAAAO,YAAA,EAAe;IACfP,EAAA,CAAAK,cAAA,iBAA8B;IAAAL,EAAA,CAAAM,MAAA,8BAAsB;IAExDN,EAFwD,CAAAO,YAAA,EAAQ,EACxD,EACF;IAKFP,EAFJ,CAAAK,cAAA,eAA0B,eACI,UACtB;IAAAL,EAAA,CAAAM,MAAA,qBAAa;IAAAN,EAAA,CAAAO,YAAA,EAAK;IACtBP,EAAA,CAAAC,SAAA,eAAmC;IACrCD,EAAA,CAAAO,YAAA,EAAM;IAGJP,EADF,CAAAK,cAAA,eAAyB,sBAQZ;IAJTL,EAAA,CAAA4B,gBAAA,2BAAA0E,uEAAAxE,MAAA;MAAA9B,EAAA,CAAAU,aAAA,CAAAqF,IAAA;MAAA,MAAAlF,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAAiC,kBAAA,CAAApB,MAAA,CAAA8D,QAAA,CAAA4B,UAAA,EAAAzE,MAAA,MAAAjB,MAAA,CAAA8D,QAAA,CAAA4B,UAAA,GAAAzE,MAAA;MAAA,OAAA9B,EAAA,CAAAe,WAAA,CAAAe,MAAA;IAAA,EAAiC;IAKjC9B,EAAA,CAAAK,cAAA,6BAAiC;IAAAL,EAAA,CAAAM,MAAA,sCAAe;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IACpEP,EAAA,CAAAK,cAAA,6BAAgC;IAAAL,EAAA,CAAAM,MAAA,+BAAa;IAC/CN,EAD+C,CAAAO,YAAA,EAAoB,EACtD;IACbP,EAAA,CAAAK,cAAA,iBAA8B;IAAAL,EAAA,CAAAM,MAAA,kBAAU;IAC1CN,EAD0C,CAAAO,YAAA,EAAQ,EAC5C;IAGJP,EADF,CAAAK,cAAA,eAAyB,sBASL;IALhBL,EAAA,CAAA4B,gBAAA,2BAAA4E,uEAAA1E,MAAA;MAAA9B,EAAA,CAAAU,aAAA,CAAAqF,IAAA;MAAA,MAAAlF,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAAiC,kBAAA,CAAApB,MAAA,CAAA8D,QAAA,CAAA8B,QAAA,EAAA3E,MAAA,MAAAjB,MAAA,CAAA8D,QAAA,CAAA8B,QAAA,GAAA3E,MAAA;MAAA,OAAA9B,EAAA,CAAAe,WAAA,CAAAe,MAAA;IAAA,EAA+B;IAE/B9B,EAAA,CAAAQ,UAAA,uBAAAkG,mEAAA5E,MAAA;MAAA9B,EAAA,CAAAU,aAAA,CAAAqF,IAAA;MAAA,MAAAlF,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAaF,MAAA,CAAA8F,qBAAA,CAAA7E,MAAA,CAA6B;IAAA,EAAC;IAI3C9B,EAAA,CAAAK,cAAA,6BAA4B;IAAAL,EAAA,CAAAM,MAAA,yBAAiB;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IACjEP,EAAA,CAAAK,cAAA,6BAAoC;IAAAL,EAAA,CAAAM,MAAA,6BAAW;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IACnEP,EAAA,CAAAK,cAAA,6BAAiC;IAAAL,EAAA,CAAAM,MAAA,0BAAQ;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IAC7DP,EAAA,CAAAK,cAAA,6BAAkC;IAAAL,EAAA,CAAAM,MAAA,2BAAS;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IAC/DP,EAAA,CAAAK,cAAA,6BAAqC;IAAAL,EAAA,CAAAM,MAAA,8BAAY;IACnDN,EADmD,CAAAO,YAAA,EAAoB,EAC1D;IACbP,EAAA,CAAAK,cAAA,iBAA8B;IAAAL,EAAA,CAAAM,MAAA,gBAAQ;IACxCN,EADwC,CAAAO,YAAA,EAAQ,EAC1C;IAENP,EAAA,CAAAkB,UAAA,KAAA0F,wCAAA,kBAAmD;IAkBrD5G,EAAA,CAAAO,YAAA,EAAM;IAKFP,EAFJ,CAAAK,cAAA,eAA0B,eACI,UACtB;IAAAL,EAAA,CAAAM,MAAA,uBAAe;IAAAN,EAAA,CAAAO,YAAA,EAAK;IACxBP,EAAA,CAAAC,SAAA,eAAmC;IACrCD,EAAA,CAAAO,YAAA,EAAM;IAIFP,EAFJ,CAAAK,cAAA,eAAsB,eACc,qBAUrB;IALTL,EAAA,CAAA4B,gBAAA,2BAAAiF,sEAAA/E,MAAA;MAAA9B,EAAA,CAAAU,aAAA,CAAAqF,IAAA;MAAA,MAAAlF,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAAiC,kBAAA,CAAApB,MAAA,CAAA8D,QAAA,CAAAlC,UAAA,EAAAX,MAAA,MAAAjB,MAAA,CAAA8D,QAAA,CAAAlC,UAAA,GAAAX,MAAA;MAAA,OAAA9B,EAAA,CAAAe,WAAA,CAAAe,MAAA;IAAA,EAAiC;IAMnC9B,EAAA,CAAAO,YAAA,EAAY;IACZP,EAAA,CAAAK,cAAA,iBAA8B;IAAAL,EAAA,CAAAM,MAAA,cAAM;IACtCN,EADsC,CAAAO,YAAA,EAAQ,EACxC;IAGJP,EADF,CAAAK,cAAA,eAAiC,sBAQpB;IAJTL,EAAA,CAAA4B,gBAAA,2BAAAkF,uEAAAhF,MAAA;MAAA9B,EAAA,CAAAU,aAAA,CAAAqF,IAAA;MAAA,MAAAlF,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAAiC,kBAAA,CAAApB,MAAA,CAAA8D,QAAA,CAAA/B,SAAA,EAAAd,MAAA,MAAAjB,MAAA,CAAA8D,QAAA,CAAA/B,SAAA,GAAAd,MAAA;MAAA,OAAA9B,EAAA,CAAAe,WAAA,CAAAe,MAAA;IAAA,EAAgC;IAKhC9B,EAAA,CAAAK,cAAA,6BAAiC;IAAAL,EAAA,CAAAM,MAAA,aAAK;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IAC1DP,EAAA,CAAAK,cAAA,6BAAiC;IAAAL,EAAA,CAAAM,MAAA,aAAK;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IAC1DP,EAAA,CAAAK,cAAA,6BAA6B;IAAAL,EAAA,CAAAM,MAAA,cAAM;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IACvDP,EAAA,CAAAK,cAAA,6BAA8B;IAAAL,EAAA,CAAAM,MAAA,kBAAU;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IAC5DP,EAAA,CAAAK,cAAA,6BAA+B;IAAAL,EAAA,CAAAM,MAAA,eAAO;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IAC1DP,EAAA,CAAAK,cAAA,6BAA+B;IAAAL,EAAA,CAAAM,MAAA,eAAO;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IAC1DP,EAAA,CAAAK,cAAA,6BAA8B;IAAAL,EAAA,CAAAM,MAAA,aAAK;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IACvDP,EAAA,CAAAK,cAAA,6BAA+B;IAAAL,EAAA,CAAAM,MAAA,gBAAQ;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IAC3DP,EAAA,CAAAK,cAAA,6BAA6B;IAAAL,EAAA,CAAAM,MAAA,aAAK;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IACtDP,EAAA,CAAAK,cAAA,6BAA8B;IAAAL,EAAA,CAAAM,MAAA,kBAAU;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IAC5DP,EAAA,CAAAK,cAAA,6BAA6B;IAAAL,EAAA,CAAAM,MAAA,cAAM;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IACvDP,EAAA,CAAAK,cAAA,6BAAiC;IAAAL,EAAA,CAAAM,MAAA,cAAM;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IAC3DP,EAAA,CAAAK,cAAA,6BAAiC;IAAAL,EAAA,CAAAM,MAAA,aAAK;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IAC1DP,EAAA,CAAAK,cAAA,6BAAiC;IAAAL,EAAA,CAAAM,MAAA,cAAK;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IAC1DP,EAAA,CAAAK,cAAA,+BAA6B;IAAAL,EAAA,CAAAM,MAAA,gBAAO;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IACxDP,EAAA,CAAAK,cAAA,+BAA6B;IAAAL,EAAA,CAAAM,MAAA,cAAK;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IACtDP,EAAA,CAAAK,cAAA,+BAA6B;IAAAL,EAAA,CAAAM,MAAA,gBAAO;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IACxDP,EAAA,CAAAK,cAAA,+BAA6B;IAAAL,EAAA,CAAAM,MAAA,eAAM;IACrCN,EADqC,CAAAO,YAAA,EAAoB,EAC5C;IACbP,EAAA,CAAAK,cAAA,kBAA8B;IAAAL,EAAA,CAAAM,MAAA,aAAI;IAEtCN,EAFsC,CAAAO,YAAA,EAAQ,EACtC,EACF;IAGJP,EADF,CAAAK,cAAA,gBAAyB,wBASZ;IALTL,EAAA,CAAA4B,gBAAA,2BAAAmF,wEAAAjF,MAAA;MAAA9B,EAAA,CAAAU,aAAA,CAAAqF,IAAA;MAAA,MAAAlF,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAAiC,kBAAA,CAAApB,MAAA,CAAA8D,QAAA,CAAAqC,WAAA,EAAAlF,MAAA,MAAAjB,MAAA,CAAA8D,QAAA,CAAAqC,WAAA,GAAAlF,MAAA;MAAA,OAAA9B,EAAA,CAAAe,WAAA,CAAAe,MAAA;IAAA,EAAkC;IAClC9B,EAAA,CAAAQ,UAAA,uBAAAyG,oEAAA;MAAAjH,EAAA,CAAAU,aAAA,CAAAqF,IAAA;MAAA,MAAAlF,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAaF,MAAA,CAAAqG,mBAAA,EAAqB;IAAA,EAAC;IAKnClH,EAAA,CAAAK,cAAA,+BAA+B;IAAAL,EAAA,CAAAM,MAAA,+BAAY;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IAC/DP,EAAA,CAAAK,cAAA,+BAAgC;IAAAL,EAAA,CAAAM,MAAA,2CAAwB;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IAC5EP,EAAA,CAAAK,cAAA,+BAAiC;IAAAL,EAAA,CAAAM,MAAA,kDAA0B;IAC7DN,EAD6D,CAAAO,YAAA,EAAoB,EACpE;IACbP,EAAA,CAAAK,cAAA,kBAA8B;IAAAL,EAAA,CAAAM,MAAA,kBAAS;IACzCN,EADyC,CAAAO,YAAA,EAAQ,EAC3C;IAoBNP,EAlBA,CAAAkB,UAAA,MAAAiG,yCAAA,mBAAsE,MAAAC,yCAAA,mBAkBC;IAoB/EpH,EAHM,CAAAO,YAAA,EAAM,EACD,EACH,EACM;IAKRP,EAHN,CAAAK,cAAA,wBAAiC,oBAClB,iBACiB,wBAIO;IAA/BL,EAAA,CAAAQ,UAAA,mBAAA6G,gEAAA;MAAArH,EAAA,CAAAU,aAAA,CAAAqF,IAAA;MAAA,MAAAlF,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAmF,kBAAA,EAAoB;IAAA,EAAC;IAC9BhG,EAAA,CAAAM,MAAA,iBACF;IAAAN,EAAA,CAAAO,YAAA,EAAa;IACbP,EAAA,CAAAK,cAAA,wBAI0B;IAAxBL,EAAA,CAAAQ,UAAA,mBAAA8G,gEAAA;MAAAtH,EAAA,CAAAU,aAAA,CAAAqF,IAAA;MAAA,MAAAlF,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAqF,WAAA,EAAa;IAAA,EAAC;IACvBlG,EAAA,CAAAC,SAAA,sBAA6C;IAC7CD,EAAA,CAAAM,MAAA,uBACF;IAGNN,EAHM,CAAAO,YAAA,EAAa,EACT,EACM,EACH;;;;;IArOCP,EAAA,CAAAoB,SAAA,IAA4B;IAA5BpB,EAAA,CAAA0C,gBAAA,YAAA7B,MAAA,CAAA8D,QAAA,CAAAf,KAAA,CAA4B;IAe5B5D,EAAA,CAAAoB,SAAA,GAA2B;IAA3BpB,EAAA,CAAA0C,gBAAA,YAAA7B,MAAA,CAAA8D,QAAA,CAAAb,IAAA,CAA2B;IAa7B9D,EAAA,CAAAoB,SAAA,GAAkC;IAAlCpB,EAAA,CAAA0C,gBAAA,YAAA7B,MAAA,CAAA8D,QAAA,CAAAZ,WAAA,CAAkC;IAoBlC/D,EAAA,CAAAoB,SAAA,GAAiC;IAAjCpB,EAAA,CAAA0C,gBAAA,YAAA7B,MAAA,CAAA8D,QAAA,CAAA4B,UAAA,CAAiC;IAejCvG,EAAA,CAAAoB,SAAA,GAA+B;IAA/BpB,EAAA,CAAA0C,gBAAA,YAAA7B,MAAA,CAAA8D,QAAA,CAAA8B,QAAA,CAA+B;IAeTzG,EAAA,CAAAoB,SAAA,IAAuB;IAAvBpB,EAAA,CAAA2B,UAAA,SAAAd,MAAA,CAAA8D,QAAA,CAAA8B,QAAA,CAAuB;IAiC3CzG,EAAA,CAAAoB,SAAA,GAAiC;IAAjCpB,EAAA,CAAA0C,gBAAA,YAAA7B,MAAA,CAAA8D,QAAA,CAAAlC,UAAA,CAAiC;IAcjCzC,EAAA,CAAAoB,SAAA,GAAgC;IAAhCpB,EAAA,CAAA0C,gBAAA,YAAA7B,MAAA,CAAA8D,QAAA,CAAA/B,SAAA,CAAgC;IAgClC5C,EAAA,CAAAoB,SAAA,IAAkC;IAAlCpB,EAAA,CAAA0C,gBAAA,YAAA7B,MAAA,CAAA8D,QAAA,CAAAqC,WAAA,CAAkC;IAaPhH,EAAA,CAAAoB,SAAA,GAAqC;IAArCpB,EAAA,CAAA2B,UAAA,SAAAd,MAAA,CAAA8D,QAAA,CAAAqC,WAAA,YAAqC;IAkBrChH,EAAA,CAAAoB,SAAA,EAAsC;IAAtCpB,EAAA,CAAA2B,UAAA,SAAAd,MAAA,CAAA8D,QAAA,CAAAqC,WAAA,aAAsC;IAkCrEhH,EAAA,CAAAoB,SAAA,GAA8B;IAA9BpB,EAAA,CAAA2B,UAAA,eAAA4F,aAAA,kBAAAA,aAAA,CAAAC,KAAA,EAA8B;;;;;;IAW1CxH,EAAA,CAAAK,cAAA,2BAC+B;IAA7BL,EAAA,CAAAQ,UAAA,mBAAAiH,uEAAA;MAAAzH,EAAA,CAAAU,aAAA,CAAAgH,IAAA;MAAA,MAAA7G,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAA8G,gBAAA,EAAkB;IAAA,EAAC;IAC9B3H,EAAA,CAAAO,YAAA,EAAkB;;;;IAF4CP,EAArB,CAAA2B,UAAA,SAAAd,MAAA,CAAA+G,WAAA,CAAoB,SAAA/G,MAAA,CAAAgH,UAAA,CAAAhH,MAAA,CAAAoD,YAAA,EAAkC;;;ADvXjG,OAAM,MAAO6D,SAAS;EAyBpB;EACQC,kBAAkBA,CAAA;IACxB;IACA,MAAMC,KAAK,GAAG,IAAIC,IAAI,EAAE;IACxBD,KAAK,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC1B,MAAMjE,YAAY,GAAG,IAAIgE,IAAI,CAAC,IAAI,CAAChE,YAAY,CAAC;IAChDA,YAAY,CAACiE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACjC,MAAMC,eAAe,GAAGlE,YAAY,CAACmE,OAAO,EAAE,KAAKJ,KAAK,CAACI,OAAO,EAAE;IAElE;IACA,IAAI,CAACD,eAAe,EAAE;MACpB,IAAI,CAAChE,UAAU,GAAG,IAAI;MACtB;IACF;IAEA,IAAI,IAAI,CAACkE,cAAc,IAAIF,eAAe,IAAI,IAAI,CAACG,MAAM,EAAE;MACzD;MACA,IAAI,CAACC,gBAAgB,CAACC,4BAA4B,CAAC,IAAI,CAACF,MAAO,CAAC,CAACG,IAAI,CACnEnJ,IAAI,CAAC,CAAC,CAAC,CACR,CAACoJ,SAAS,CAAC;QACVC,IAAI,EAAGC,UAAU,IAAI;UACnB,IAAIA,UAAU,IAAIA,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;YACvC,MAAMC,SAAS,GAAGF,UAAU,CAAC,CAAC,CAAC;YAE/B;YACA,IAAI,CAACG,eAAe,CAACC,SAAS,EAAE,CAC7BC,IAAI,CAAC,sBAAsB,CAAC,CAC5BC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,IAAI,EAAEL,SAAS,CAACM,gBAAgB,CAAC,CACpCC,MAAM,EAAE,CACRC,IAAI,CAACC,QAAQ,IAAG;cACf,IAAIA,QAAQ,CAACC,KAAK,EAAE;gBAClB;cACF;cAEA,MAAMC,YAAY,GAAGF,QAAQ,CAACG,IAAI;cAElC;cACA,IAAI,CAACvF,UAAU,GAAG;gBAChBwF,EAAE,EAAEb,SAAS,CAACa,EAAG;gBACjBnF,aAAa,EAAE;kBACbmF,EAAE,EAAEb,SAAS,CAACM,gBAAiB;kBAC/BtF,IAAI,EAAE2F,YAAY,CAAC3F,IAAI,IAAI,kBAAkB;kBAC7CC,WAAW,EAAE0F,YAAY,CAAC1F,WAAW,IAAI,gCAAgC;kBACzEtB,UAAU,EAAEgH,YAAY,CAAChH,UAAU,IAAI,CAAC;kBACxCG,SAAS,EAAE6G,YAAY,CAAC7G,SAAS,IAAI;iBACtC;gBACDQ,MAAM,EAAE0F,SAAS,CAAC1F,MAAM,IAAI,CAAC;gBAC7BO,SAAS,EAAEmF,SAAS,CAACnF,SAAS,IAAI,KAAK;gBACvCzB,cAAc,EAAE4G,SAAS,CAAC5G,cAAc,IAAI,CAAC;gBAC7C0B,KAAK,EAAE6F,YAAY,CAAC7F,KAAK,IAAI;eAC9B;YACH,CAAC,CAAC;UACN,CAAC,MAAM;YACL,IAAI,CAACO,UAAU,GAAG,IAAI;UACxB;QACF,CAAC;QACDqF,KAAK,EAAEA,CAAA,KAAK;UACV,IAAI,CAACrF,UAAU,GAAG,IAAI;QACxB;OACD,CAAC;IACJ;EACF;EAqCAyF,YAAA;IA3HA;IACA,KAAAC,KAAK,GAA4BzK,EAAE,CAAC,IAAI,CAAC;IACzC,KAAAkJ,MAAM,GAAkB,IAAI;IAE5B,KAAAD,cAAc,GAAG,IAAI;IAErB;IACA,KAAApE,YAAY,GAAS,IAAIgE,IAAI,EAAE;IAC/B,KAAA6B,SAAS,GAAe,EAAE;IAC1B,KAAAC,QAAQ,GAAa,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC/D,KAAAC,UAAU,GAAW,OAAO;IAC5B,KAAAC,UAAU,GAAW,CAAC;IAGtB;IACA,KAAAC,MAAM,GAAmB,EAAE;IAC3B,KAAA/F,UAAU,GAAsB,IAAI;IAEpC;IACQ,KAAAgG,UAAU,GAA0C,EAAE;IAE9D;IACQ,KAAAC,aAAa,GAAG,KAAK;IAkE7B;IACA,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAA1F,QAAQ,GAAG,IAAI,CAAC2F,aAAa,EAAE;IAC/B,KAAAxF,oBAAoB,GAAG,KAAK;IAE5B;IACA,KAAAyF,eAAe,GAAG,KAAK;IACvB,KAAA3C,WAAW,GAAgB,IAAI;IAC/B,KAAA4C,qBAAqB,GAAa,EAAE;IAEpC;IACA,KAAAjF,QAAQ,GAAG,CACT;MAAEJ,KAAK,EAAE,KAAK;MAAEE,KAAK,EAAE;IAAI,CAAE,EAC7B;MAAEF,KAAK,EAAE,KAAK;MAAEE,KAAK,EAAE;IAAI,CAAE,EAC7B;MAAEF,KAAK,EAAE,KAAK;MAAEE,KAAK,EAAE;IAAI,CAAE,EAC7B;MAAEF,KAAK,EAAE,KAAK;MAAEE,KAAK,EAAE;IAAI,CAAE,EAC7B;MAAEF,KAAK,EAAE,KAAK;MAAEE,KAAK,EAAE;IAAI,CAAE,EAC7B;MAAEF,KAAK,EAAE,KAAK;MAAEE,KAAK,EAAE;IAAI,CAAE,EAC7B;MAAEF,KAAK,EAAE,KAAK;MAAEE,KAAK,EAAE;IAAI,CAAE,CAC9B;IACD,KAAAQ,SAAS,GAAG4E,KAAK,CAACxB,IAAI,CAAC;MAAEJ,MAAM,EAAE;IAAE,CAAE,EAAE,CAAC6B,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC;IACvD,KAAAC,kBAAkB,GAAa,EAAE;IACjC,KAAAC,mBAAmB,GAAa,EAAE;IAElC;IACQ,KAAAC,YAAY,GAAGtM,MAAM,CAACI,YAAY,CAAC;IACnC,KAAA2J,gBAAgB,GAAG/J,MAAM,CAACK,gBAAgB,CAAC;IAC3C,KAAAkM,WAAW,GAAGvM,MAAM,CAACM,WAAW,CAAC;IACjC,KAAAiK,eAAe,GAAGvK,MAAM,CAACO,eAAe,CAAC;IACzC,KAAAiM,KAAK,GAAGxM,MAAM,CAACkB,cAAc,CAAC;IAC9B,KAAAuL,MAAM,GAAGzM,MAAM,CAACmB,MAAM,CAAC;IACvB,KAAAuL,kBAAkB,GAAG1M,MAAM,CAACoB,kBAAkB,CAAC;IAC/C,KAAAuL,gBAAgB,GAAG3M,MAAM,CAACsB,uBAAuB,CAAC;IAClD,KAAAsL,aAAa,GAAG,KAAK,CAAC,CAAC;IAykB/B;IACQ,KAAAC,iBAAiB,GAAgE,EAAE;IAmL3F;IACQ,KAAAC,cAAc,GAAG,KAAK;IAqI9B;IACQ,KAAAC,gBAAgB,GAAmC,EAAE;IAoC7D;IACQ,KAAAC,gBAAgB,GAAmC,EAAE;IAgY7D;IACQ,KAAAC,oBAAoB,GAAmC,EAAE;IAvyC/D;IACA,IAAI,CAACT,KAAK,CAACU,WAAW,CAAChD,SAAS,CAACiD,MAAM,IAAG;MACxC,MAAMC,SAAS,GAAGD,MAAM,CAAC,MAAM,CAAC;MAChC,MAAME,eAAe,GAAGF,MAAM,CAAC,aAAa,CAAC;MAE7CG,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEH,SAAS,CAAC;MAC/DE,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEF,eAAe,CAAC;MAE5E;MACA,IAAIA,eAAe,KAAKG,SAAS,EAAE;QACjC,IAAI;UACF,IAAI,CAAC/B,UAAU,GAAGgC,QAAQ,CAACJ,eAAe,CAAC;UAC3CC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE,IAAI,CAAC9B,UAAU,CAAC;QAChE,CAAC,CAAC,OAAOT,KAAK,EAAE;UACdsC,OAAO,CAACtC,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;UAC7D,IAAI,CAACS,UAAU,GAAG,CAAC;QACrB;MACF,CAAC,MAAM;QACL,IAAI,CAACA,UAAU,GAAG,CAAC;MACrB;MAEA;MACA,IAAI2B,SAAS,EAAE;QACb,IAAI;UACF;UACA,IAAI,qBAAqB,CAACM,IAAI,CAACN,SAAS,CAAC,EAAE;YACzC,IAAI,CAAC3H,YAAY,GAAG,IAAIgE,IAAI,CAAC2D,SAAS,CAAC;YACvCE,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE,IAAI,CAAC9H,YAAY,CAAC;UAC5E,CAAC,MAAM;YACL6H,OAAO,CAACtC,KAAK,CAAC,8CAA8C,EAAEoC,SAAS,CAAC;YACxE,IAAI,CAAC3H,YAAY,GAAG,IAAIgE,IAAI,EAAE,CAAC,CAAC;UAClC;QACF,CAAC,CAAC,OAAOuB,KAAK,EAAE;UACdsC,OAAO,CAACtC,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;UACrE,IAAI,CAACvF,YAAY,GAAG,IAAIgE,IAAI,EAAE,CAAC,CAAC;QAClC;MACF,CAAC,MAAM;QACL,IAAI,CAAChE,YAAY,GAAG,IAAIgE,IAAI,EAAE,CAAC,CAAC;MAClC;MAEA;MACA,IAAI,CAACkE,iBAAiB,EAAE;MAExB;MACA,IAAI,CAACC,gBAAgB,EAAE;MAEvB;MACA,IAAI,IAAI,CAAC9D,MAAM,EAAE;QACf,IAAI,CAAC+D,QAAQ,EAAE;MACjB;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACvD,eAAe,CAACwD,YAAY,CAAC7D,SAAS,CAAC8D,QAAQ,IAAG;MAG7E,IAAI,CAACA,QAAQ,EAAE;QACbV,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QACpE;QACA;MACF;MAEA;MACA,IAAI,CAACzD,MAAM,GAAGkE,QAAQ,CAAC7C,EAAE;MAGzB;MACA,IAAI,CAACoB,WAAW,CAAC0B,WAAW,CAACD,QAAQ,CAAC7C,EAAE,CAAC,CAACjB,SAAS,CAACgE,QAAQ,IAAG;QAC7D,IAAI,CAACA,QAAQ,EAAE;UACbZ,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;UACjE;UACA;QACF;QAEAD,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEW,QAAQ,CAAC;QACrD,IAAI,CAACL,QAAQ,EAAE;MACjB,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAACxC,KAAK,GAAG,IAAI,CAACd,eAAe,CAACwD,YAAY,CAAC9D,IAAI,CACjDpJ,SAAS,CAACmN,QAAQ,IAAG;MACnB,IAAI,CAACA,QAAQ,EAAE;QACb,OAAOpN,EAAE,CAAC,IAAI,CAAC;MACjB;MAEA,OAAO,IAAI,CAAC2L,WAAW,CAAC0B,WAAW,CAACD,QAAQ,CAAC7C,EAAE,CAAC;IAClD,CAAC,CAAC,CACH;IAED;IACA,MAAMgD,oBAAoB,GAAG,IAAI,CAAC9C,KAAK,CAACnB,SAAS,CAAC;MAChDC,IAAI,EAAGiE,IAAI,IAAI;QACb,IAAIA,IAAI,EAAE;UACR,IAAI,CAACvE,cAAc,GAAGuE,IAAI,CAACC,iBAAiB;UAC5C,IAAI,CAACjF,WAAW,GAAGgF,IAAI;QACzB;MACF;KACD,CAAC;IAEF;IACA,IAAI,CAACN,gBAAgB,GAAG,IAAIrN,YAAY,EAAE;IAC1C,IAAI,CAACqN,gBAAgB,CAACQ,GAAG,CAACH,oBAAoB,CAAC;EACjD;EAEAI,QAAQA,CAAA;IACN;IACA,IAAI,CAACZ,iBAAiB,EAAE;IAExB;IACAa,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,eAAe,EAAE;IACxB,CAAC,EAAE,CAAC,CAAC;IAEL;IACA,IAAI;MACF;MACA,MAAMjF,KAAK,GAAG,IAAIC,IAAI,EAAE;MACxB,MAAMiF,QAAQ,GAAG,IAAI,CAACrF,UAAU,CAACG,KAAK,CAAC;MAEvC;MACA,MAAMmF,OAAO,GAAa,EAAE;MAC5B,KAAK,IAAIxC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyC,YAAY,CAACvE,MAAM,EAAE8B,CAAC,EAAE,EAAE;QAC5C,MAAM0C,GAAG,GAAGD,YAAY,CAACC,GAAG,CAAC1C,CAAC,CAAC;QAC/B,IAAI0C,GAAG,EAAE;UACPF,OAAO,CAACG,IAAI,CAACD,GAAG,CAAC;QACnB;MACF;MAEA;MACAF,OAAO,CAACI,OAAO,CAACF,GAAG,IAAG;QACpB,IAAIA,GAAG,CAACG,UAAU,CAAC,oBAAoB,CAAC,IAAIH,GAAG,KAAK,qBAAqBH,QAAQ,EAAE,EAAE;UACnFE,YAAY,CAACK,UAAU,CAACJ,GAAG,CAAC;QAC9B;MACF,CAAC,CAAC;MAEF;MACA,MAAMK,qBAAqB,GAAGN,YAAY,CAACO,OAAO,CAAC,qBAAqBT,QAAQ,EAAE,CAAC;MAEnF;MACA,IAAI,CAAC1C,qBAAqB,GAAG,EAAE;MAC/B,IAAIkD,qBAAqB,EAAE;QACzB,IAAI,CAAClD,qBAAqB,CAAC8C,IAAI,CAACJ,QAAQ,CAAC;MAC3C;IACF,CAAC,CAAC,OAAO1D,KAAK,EAAE;MACd,IAAI,CAACgB,qBAAqB,GAAG,EAAE;IACjC;EACF;EAEMoD,gBAAgBA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACpB;MACA;MACA,MAAMtB,QAAQ,GAAGqB,KAAI,CAAC9E,eAAe,CAACgF,YAAY,CAAC5I,KAAK;MAExD,IAAI,CAACqH,QAAQ,EAAE;QACbV,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QACpE;MACF;MAEA;MACA8B,KAAI,CAAC9C,WAAW,CAACiD,gBAAgB,CAACxB,QAAQ,CAAC,CAAC9D,SAAS,CAACgE,QAAQ,IAAG;QAC/D,IAAI,CAACA,QAAQ,EAAE;UACbmB,KAAI,CAAC5C,MAAM,CAACgD,aAAa,CAAC,SAAS,CAAC;UACpC;QACF;QAEA;QACA,IAAIC,OAAO,GAAGxB,QAAQ,CAACyB,mBAAmB,GAAG,IAAIlG,IAAI,CAACyE,QAAQ,CAACyB,mBAAmB,CAAC,GAAG,IAAI;QAC1F,MAAMC,WAAW,GAAG,IAAInG,IAAI,EAAE;QAE9B;QACA,IAAIoG,WAAW,GAAG,KAAK;QACvB,IAAIH,OAAO,YAAYjG,IAAI,EAAE;UAC3BoG,WAAW,GAAGH,OAAO,GAAGE,WAAW;QACrC;QAEA,IAAI,CAACC,WAAW,EAAE;UAChB;UACA,IAAIR,KAAI,CAACzC,aAAa,EAAE;UACxByC,KAAI,CAACzC,aAAa,GAAG,IAAI;UAEzB4B,UAAU,CAAC,MAAK;YACda,KAAI,CAAC5C,MAAM,CAACgD,aAAa,CAAC,UAAU,CAAC;YACrCjB,UAAU,CAAC,MAAK;cACda,KAAI,CAACzC,aAAa,GAAG,KAAK;YAC5B,CAAC,EAAE,IAAI,CAAC;UACV,CAAC,EAAE,GAAG,CAAC;UACP;QACF;QAEA;QACA,MAAMkD,OAAO,GAAGT,KAAI,CAAChG,UAAU,CAACgG,KAAI,CAAC5J,YAAY,CAAC;QAClD,IAAI4J,KAAI,CAAC1D,UAAU,CAACmE,OAAO,CAAC,EAAE;UAC5B;UACAT,KAAI,CAAC3D,MAAM,GAAG2D,KAAI,CAAC1D,UAAU,CAACmE,OAAO,CAAC;UAEtC;UACAC,qBAAqB,CAAC,MAAK;YACzBV,KAAI,CAACW,2BAA2B,EAAE;UACpC,CAAC,CAAC;UAEF;UACAX,KAAI,CAAC9F,kBAAkB,EAAE;QAC3B,CAAC,MAAM;UACL;UACA8F,KAAI,CAACxB,QAAQ,EAAE;QACjB;MACF,CAAC,CAAC;MAEF;MACA,MAAMrB,KAAK,GAAG6C,KAAI,CAAC5C,MAAM,CAACwD,GAAG;MAC7B,MAAM7C,SAAS,GAAGiC,KAAI,CAAChG,UAAU,CAACgG,KAAI,CAAC5J,YAAY,CAAC;MAEpD,IAAI+G,KAAK,KAAK,QAAQ,EAAE;QACtB;QACA6C,KAAI,CAAC5C,MAAM,CAACyD,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;UAC/BhD,WAAW,EAAE;YACXiD,IAAI,EAAE/C,SAAS;YACfgD,WAAW,EAAEf,KAAI,CAAC5D,UAAU,KAAK,CAAC,GAAG4D,KAAI,CAAC5D,UAAU,GAAG;WACxD;UACD4E,UAAU,EAAE;SACb,CAAC;MACJ;IAAC;EACH;EAGA;EACAL,2BAA2BA,CAAA;IACzB;IACAD,qBAAqB,CAAC,MAAK;MACzB,MAAMO,OAAO,GAAGC,QAAQ,CAACC,gBAAgB,CAAC,kBAAkB,CAAC;MAC7D,IAAIF,OAAO,CAACjG,MAAM,KAAK,CAAC,EAAE;QACxB;MACF;MAEAiG,OAAO,CAACvB,OAAO,CAAC0B,MAAM,IAAG;QACvB,IAAIA,MAAM,YAAYC,gBAAgB,EAAE;UACtC;UACA,MAAMC,aAAa,GAAGF,MAAM,CAACG,YAAY,CAAC,eAAe,CAAC;UAC1D,IAAI,CAACD,aAAa,EAAE;YAClB;UACF;UAEA;UACA,MAAME,WAAW,GAAGpD,QAAQ,CAACgD,MAAM,CAAC9J,KAAK,CAAC;UAC1C,MAAMmK,QAAQ,GAAGrD,QAAQ,CAACgD,MAAM,CAACM,GAAG,CAAC;UACrC,MAAMC,QAAQ,GAAGvD,QAAQ,CAACgD,MAAM,CAACQ,GAAG,CAAC;UAErC;UACA,MAAMC,UAAU,GAAGF,QAAQ,GAAGF,QAAQ,GACnC,CAACD,WAAW,GAAGC,QAAQ,KAAKE,QAAQ,GAAGF,QAAQ,CAAC,GAAI,GAAG,GAAG,CAAC;UAE9D;UACAL,MAAM,CAACU,KAAK,CAACC,UAAU,GACrB,iDAAiDF,UAAU,cAAcA,UAAU,kBAAkB;UAEvG;UACAT,MAAM,CAACY,YAAY,CAAC,oBAAoB,EAAEZ,MAAM,CAAC9J,KAAK,CAAC;QACzD,CAAC,MAAM,IAAI8J,MAAM,YAAYa,WAAW,IAAIb,MAAM,CAACc,OAAO,KAAK,WAAW,EAAE;UAC1E;UACA,MAAMZ,aAAa,GAAGF,MAAM,CAACG,YAAY,CAAC,eAAe,CAAC;UAC1D,IAAI,CAACD,aAAa,EAAE;YAClB;UACF;UAEA;UACA,MAAMa,SAAS,GAAGf,MAAM,CAACG,YAAY,CAAC,OAAO,CAAC,IAAI,GAAG;UACrD,MAAMa,OAAO,GAAGhB,MAAM,CAACG,YAAY,CAAC,KAAK,CAAC,IAAI,GAAG;UACjD,MAAMc,OAAO,GAAGjB,MAAM,CAACG,YAAY,CAAC,KAAK,CAAC,IAAI,KAAK;UAEnD,MAAMC,WAAW,GAAGpD,QAAQ,CAAC+D,SAAS,CAAC;UACvC,MAAMV,QAAQ,GAAGrD,QAAQ,CAACgE,OAAO,CAAC;UAClC,MAAMT,QAAQ,GAAGvD,QAAQ,CAACiE,OAAO,CAAC;UAElC;UACA,MAAMR,UAAU,GAAGF,QAAQ,GAAGF,QAAQ,GACnC,CAACD,WAAW,GAAGC,QAAQ,KAAKE,QAAQ,GAAGF,QAAQ,CAAC,GAAI,GAAG,GAAG,CAAC;UAE9D;UACAL,MAAM,CAACU,KAAK,CAACQ,WAAW,CAAC,kBAAkB,EAAE,GAAGT,UAAU,GAAG,CAAC;UAE9D;UACAT,MAAM,CAACY,YAAY,CAAC,oBAAoB,EAAER,WAAW,CAACe,QAAQ,EAAE,CAAC;QACnE;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAC,gBAAgBA,CAAA;IACdvE,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;EACnD;EAEAuE,WAAWA,CAAA;IACTxE,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC5C,IAAI,IAAI,CAACO,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACiE,WAAW,EAAE;IACrC;EACF;EAEMlE,QAAQA,CAAA;IAAA,IAAAmE,MAAA;IAAA,OAAA1C,iBAAA;MACZ,IAAI,CAAC0C,MAAI,CAAClI,MAAM,EAAE;QAChB;MACF;MAEA;MACAkI,MAAI,CAACpE,gBAAgB,EAAE;MAEvB;MACA,MAAMkC,OAAO,GAAGkC,MAAI,CAAC3I,UAAU,CAAC2I,MAAI,CAACvM,YAAY,CAAC;MAClD,IAAIuM,MAAI,CAACrG,UAAU,CAACmE,OAAO,CAAC,EAAE;QAC5B;QACAkC,MAAI,CAACtG,MAAM,GAAGsG,MAAI,CAACrG,UAAU,CAACmE,OAAO,CAAC;QAEtC;QACAC,qBAAqB,CAAC,MAAK;UACzBiC,MAAI,CAAChC,2BAA2B,EAAE;QACpC,CAAC,CAAC;QAEF;QACAgC,MAAI,CAACzI,kBAAkB,EAAE;QAEzB;MACF;MAEA;MACA,MAAMC,KAAK,GAAG,IAAIC,IAAI,EAAE;MACxBD,KAAK,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC1B,MAAMjE,YAAY,GAAG,IAAIgE,IAAI,CAACuI,MAAI,CAACvM,YAAY,CAAC;MAChDA,YAAY,CAACiE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACjC,MAAMC,eAAe,GAAGlE,YAAY,CAACmE,OAAO,EAAE,KAAKJ,KAAK,CAACI,OAAO,EAAE;MAElE0D,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEyE,MAAI,CAAC3I,UAAU,CAAC2I,MAAI,CAACvM,YAAY,CAAC,CAAC;MACpF,IAAIkE,eAAe,EAAE;QACnB;QACA,MAAMsI,eAAe,GAAGD,MAAI,CAAC3I,UAAU,CAACG,KAAK,CAAC;QAC9C,IAAI;UACF,MAAM;YAAE7C,KAAK,EAAEuL;UAAqB,CAAE,SAASF,MAAI,CAACtF,kBAAkB,CAACyF,GAAG,CAAC,yBAAyB,CAAC;UAErG,IAAID,qBAAqB,KAAKD,eAAe,EAAE;YAC7C3E,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;YAEvE;YACA,MAAMxM,cAAc,CAACiR,MAAI,CAAC1F,YAAY,CAAC8F,SAAS,CAACJ,MAAI,CAAClI,MAAO,CAAC,CAACG,IAAI,CACjEnJ,IAAI,CAAC,CAAC,CAAC,EACPD,SAAS;cAAA,IAAAwR,IAAA,GAAA/C,iBAAA,CAAC,WAAM5D,MAAM,EAAG;gBACvB;gBACA,KAAK,MAAM4G,KAAK,IAAI5G,MAAM,EAAE;kBAC1B,IAAI4G,KAAK,CAACnH,EAAE,EAAE;oBACZ,MAAM6G,MAAI,CAAC1F,YAAY,CAACiG,eAAe,CAACD,KAAK,CAACnH,EAAE,CAAC;kBACnD;gBACF;gBAEA;gBACA,MAAM6G,MAAI,CAAC1F,YAAY,CAACkG,+BAA+B,EAAE;gBAEzD;gBACA,OAAO9G,MAAM;cACf,CAAC;cAAA,iBAAA+G,EAAA;gBAAA,OAAAJ,IAAA,CAAAK,KAAA,OAAAC,SAAA;cAAA;YAAA,IAAC,CACH,CAAC;YAEF;UACF,CAAC,MAAM;YACLrF,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;UAChE;QACF,CAAC,CAAC,OAAOvC,KAAK,EAAE;UACdsC,OAAO,CAACtC,KAAK,CAAC,oDAAoD,EAAEA,KAAK,CAAC;UAE1E;QACF;QAEA;QACA,IAAIgH,MAAI,CAACnI,cAAc,EAAE;UACvBmI,MAAI,CAACjI,gBAAgB,CAAC6I,0BAA0B,CAACZ,MAAI,CAAClI,MAAM,EAAEkI,MAAI,CAACvM,YAAY,CAAC,CAC7EyE,SAAS,CAAC;YACTc,KAAK,EAAGA,KAAK,IAAI;cACfsC,OAAO,CAACtC,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;YAChE;WACD,CAAC;QACN;MACF;MACA;MACAgH,MAAI,CAAC1F,YAAY,CAAC8F,SAAS,CAACJ,MAAI,CAAClI,MAAM,CAAC,CAACG,IAAI,CAC3CnJ,IAAI,CAAC,CAAC,CAAC,EACPD,SAAS,CAAC6K,MAAM,IAAG;QACjB;QACA,MAAMmH,cAAc,GAAGb,MAAI,CAACc,mBAAmB,CAACpH,MAAM,EAAEsG,MAAI,CAACvM,YAAY,CAAC;QAE1E,IAAIoN,cAAc,CAACxI,MAAM,KAAK,CAAC,EAAE;UAC/B,OAAOzJ,EAAE,CAAC,EAAE,CAAC;QACf;QAEA;QACA,MAAMmS,oBAAoB,GAAG,CAAC,GAAGF,cAAc,CAAC,CAACG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;UAC7D,IAAID,CAAC,CAACE,UAAU,IAAID,CAAC,CAACC,UAAU,EAAE;YAChC,OAAO,IAAI1J,IAAI,CAACyJ,CAAC,CAACC,UAAU,CAAC,CAACvJ,OAAO,EAAE,GAAG,IAAIH,IAAI,CAACwJ,CAAC,CAACE,UAAU,CAAC,CAACvJ,OAAO,EAAE;UAC5E;UACA,OAAOqJ,CAAC,CAAC9H,EAAE,IAAI+H,CAAC,CAAC/H,EAAE,GAAG8H,CAAC,CAAC9H,EAAE,CAACiI,aAAa,CAACF,CAAC,CAAC/H,EAAE,CAAC,GAAG,CAAC;QACpD,CAAC,CAAC;QAEF;QACA,OAAO6G,MAAI,CAAC1F,YAAY,CAAC+G,uBAAuB,CAACrB,MAAI,CAAClI,MAAO,EAAEkI,MAAI,CAACvM,YAAY,CAAC,CAACwE,IAAI,CACpFnJ,IAAI,CAAC,CAAC,CAAC,EACPD,SAAS,CAACyS,WAAW,IAAG;UACtB;UACA,MAAMC,cAAc,GAAyC,EAAE;UAC/DD,WAAW,CAACvE,OAAO,CAACyE,QAAQ,IAAG;YAC7BD,cAAc,CAACC,QAAQ,CAACC,QAAQ,CAAC,GAAGD,QAAQ;UAC9C,CAAC,CAAC;UAEF;UACA;UACA,IAAI7J,eAAe,EAAE;YACnB;YACA,MAAMsI,eAAe,GAAGD,MAAI,CAAC3I,UAAU,CAACG,KAAK,CAAC;YAC9C,OAAOwI,MAAI,CAACtF,kBAAkB,CAACyF,GAAG,CAAC,yBAAyB,CAAC,CAACrH,IAAI,CAAC,CAAC;cAAEnE,KAAK,EAAEuL;YAAqB,CAAE,KAAI;cACtG,IAAIA,qBAAqB,KAAKD,eAAe,EAAE;gBAC7C3E,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;gBAEvE;gBACA,OAAOyE,MAAI,CAACrF,gBAAgB,CAAC+G,gBAAgB,CAAC1B,MAAI,CAAClI,MAAO,EAAEiJ,oBAAoB,CAAC,CAACjI,IAAI,CAAC6I,OAAO,IAAG;kBAC/F;kBACA,OAAOZ,oBAAoB,CAACpS,GAAG,CAAC2R,KAAK,IAAG;oBACtC,MAAMkB,QAAQ,GAAGD,cAAc,CAACjB,KAAK,CAACnH,EAAG,CAAC;oBAC1C,MAAMyI,gBAAgB,GAAGD,OAAO,CAACrB,KAAK,CAACnH,EAAG,CAAC,IAAI,CAAC;oBAEhD;oBACA6G,MAAI,CAAC1F,YAAY,CAACuH,iBAAiB,CAACvB,KAAK,CAACnH,EAAG,EAAEyI,gBAAgB,CAAC,CAAC1J,SAAS,EAAE;oBAE5E,OAAO;sBACL,GAAGoI,KAAK;sBACRnN,SAAS,EAAE,CAAAqO,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAErO,SAAS,KAAI,KAAK;sBACvCzB,cAAc,EAAE,CAAA8P,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE9P,cAAc,KAAI,CAAC;sBAC7CkB,MAAM,EAAEgP;qBACO;kBACnB,CAAC,CAAC;gBACJ,CAAC,CAAC,CAAC9I,IAAI,CAACgJ,MAAM,IAAG;kBACf;kBACA9B,MAAI,CAACtF,kBAAkB,CAACqH,GAAG,CAAC,yBAAyB,EAAE9B,eAAe,CAAC;kBACvE,OAAO6B,MAAM;gBACf,CAAC,CAAC;cACJ,CAAC,MAAM;gBACLxG,OAAO,CAACC,GAAG,CAAC,wEAAwE,CAAC;gBAErF;gBACA,OAAOwF,oBAAoB,CAACpS,GAAG,CAAC2R,KAAK,IAAG;kBACtC,MAAMkB,QAAQ,GAAGD,cAAc,CAACjB,KAAK,CAACnH,EAAG,CAAC;kBAE1C,OAAO;oBACL,GAAGmH,KAAK;oBACRnN,SAAS,EAAE,CAAAqO,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAErO,SAAS,KAAI,KAAK;oBACvCzB,cAAc,EAAE,CAAA8P,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE9P,cAAc,KAAI,CAAC;oBAC7CkB,MAAM,EAAE0N,KAAK,CAAC1N,MAAM,IAAI;mBACT;gBACnB,CAAC,CAAC;cACJ;YACF,CAAC,CAAC,CAACoP,KAAK,CAAChJ,KAAK,IAAG;cACfsC,OAAO,CAACtC,KAAK,CAAC,oDAAoD,EAAEA,KAAK,CAAC;cAE1E;cACA,OAAO+H,oBAAoB,CAACpS,GAAG,CAAC2R,KAAK,IAAG;gBACtC,MAAMkB,QAAQ,GAAGD,cAAc,CAACjB,KAAK,CAACnH,EAAG,CAAC;gBAE1C,OAAO;kBACL,GAAGmH,KAAK;kBACRnN,SAAS,EAAE,CAAAqO,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAErO,SAAS,KAAI,KAAK;kBACvCzB,cAAc,EAAE,CAAA8P,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE9P,cAAc,KAAI,CAAC;kBAC7CkB,MAAM,EAAE0N,KAAK,CAAC1N,MAAM,IAAI;iBACT;cACnB,CAAC,CAAC;YACJ,CAAC,CAAC;UACJ,CAAC,MAAM;YACL;YACA,OAAOqP,OAAO,CAACC,OAAO,CAACnB,oBAAoB,CAACpS,GAAG,CAAC2R,KAAK,IAAG;cACtD,MAAMkB,QAAQ,GAAGD,cAAc,CAACjB,KAAK,CAACnH,EAAG,CAAC;cAE1C,OAAO;gBACL,GAAGmH,KAAK;gBACRnN,SAAS,EAAE,CAAAqO,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAErO,SAAS,KAAI,KAAK;gBACvCzB,cAAc,EAAE,CAAA8P,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE9P,cAAc,KAAI,CAAC;gBAC7CkB,MAAM,EAAE,CAAC,CAAC;eACK;YACnB,CAAC,CAAC,CAAC;UACL;QACF,CAAC,CAAC,CACH;MAGH,CAAC,CAAC,CACH,CAACsF,SAAS,CAAC;QACVC,IAAI,EAAGgK,kBAAkB,IAAI;UAC3B;UACA,MAAMC,YAAY,GAAG,CAAC,GAAGD,kBAAkB,CAAC,CAACnB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;YACzD,IAAID,CAAC,CAACE,UAAU,IAAID,CAAC,CAACC,UAAU,EAAE;cAChC,OAAO,IAAI1J,IAAI,CAACyJ,CAAC,CAACC,UAAU,CAAC,CAACvJ,OAAO,EAAE,GAAG,IAAIH,IAAI,CAACwJ,CAAC,CAACE,UAAU,CAAC,CAACvJ,OAAO,EAAE;YAC5E;YACA,OAAOqJ,CAAC,CAAC9H,EAAE,IAAI+H,CAAC,CAAC/H,EAAE,GAAG8H,CAAC,CAAC9H,EAAE,CAACiI,aAAa,CAACF,CAAC,CAAC/H,EAAE,CAAC,GAAG,CAAC;UACpD,CAAC,CAAC;UAEF;UACA6G,MAAI,CAACqC,uBAAuB,CAACD,YAAY,CAAC;UAE1C;UACApC,MAAI,CAACtG,MAAM,GAAG0I,YAAY;UAE1B;UACA,MAAMtE,OAAO,GAAGkC,MAAI,CAAC3I,UAAU,CAAC2I,MAAI,CAACvM,YAAY,CAAC;UAClDuM,MAAI,CAACrG,UAAU,CAACmE,OAAO,CAAC,GAAGsE,YAAY;UAEvC;UACApC,MAAI,CAACsC,sBAAsB,EAAE;UAE7B;UACAvE,qBAAqB,CAAC,MAAK;YACzBiC,MAAI,CAAChC,2BAA2B,EAAE;UACpC,CAAC,CAAC;UAEF;UACAgC,MAAI,CAACzI,kBAAkB,EAAE;QAC3B,CAAC;QACDyB,KAAK,EAAGA,KAAK,IAAI;UACfsC,OAAO,CAACtC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC/C;OACD,CAAC;IAAC;EAEL;EAEA2C,iBAAiBA,CAAA;IACf,MAAMnE,KAAK,GAAG,IAAIC,IAAI,EAAE;IAExB;IACA;IACA,MAAM8K,UAAU,GAAG/K,KAAK,CAACgL,MAAM,EAAE,CAAC,CAAC;IACnC,MAAMC,cAAc,GAAGF,UAAU,KAAK,CAAC,GAAG,CAAC,GAAGA,UAAU,GAAG,CAAC,CAAC,CAAC;IAC9D,MAAMG,WAAW,GAAG,IAAIjL,IAAI,CAACD,KAAK,CAAC;IACnCkL,WAAW,CAACC,OAAO,CAACnL,KAAK,CAACoL,OAAO,EAAE,GAAGH,cAAc,GAAI,CAAC,GAAG,IAAI,CAAChJ,UAAW,CAAC;IAE7E,IAAI,CAACH,SAAS,GAAG,EAAE;IACnB,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1B,MAAMgE,IAAI,GAAG,IAAI1G,IAAI,CAACiL,WAAW,CAAC;MAClCvE,IAAI,CAACwE,OAAO,CAACD,WAAW,CAACE,OAAO,EAAE,GAAGzI,CAAC,CAAC;MAEvC,MAAM0I,UAAU,GAAG,IAAI,CAACxL,UAAU,CAAC8G,IAAI,CAAC;MACxC,MAAM2E,OAAO,GAAG,IAAI,CAACtP,SAAS,CAAC2K,IAAI,EAAE3G,KAAK,CAAC;MAC3C,MAAMuL,UAAU,GAAG,IAAI,CAACvP,SAAS,CAAC2K,IAAI,EAAE,IAAI,CAAC1K,YAAY,CAAC;MAC1D,MAAMuP,QAAQ,GAAG7E,IAAI,GAAG3G,KAAK;MAE7B;MACA,MAAMsG,OAAO,GAAG+E,UAAU;MAC1B,IAAII,WAAW,GAAG,CAAC;MACnB,IAAIC,eAAe,GAAG,CAAC;MACvB,IAAIC,oBAAoB,GAAG,CAAC;MAE5B,IAAI,IAAI,CAACtI,iBAAiB,CAACiD,OAAO,CAAC,EAAE;QACnC,MAAMsF,MAAM,GAAG,IAAI,CAACvI,iBAAiB,CAACiD,OAAO,CAAC;QAC9CmF,WAAW,GAAGG,MAAM,CAACC,KAAK;QAC1BH,eAAe,GAAGE,MAAM,CAACjQ,SAAS;QAClCgQ,oBAAoB,GAAGF,WAAW,GAAG,CAAC,GAClCK,IAAI,CAACC,KAAK,CAAEL,eAAe,GAAGD,WAAW,GAAI,GAAG,CAAC,GACjD,CAAC;MACP;MAEA,IAAI,CAAC3J,SAAS,CAACwD,IAAI,CAAC;QAClBqB,IAAI,EAAE0E,UAAU;QAChBW,GAAG,EAAErF,IAAI,CAACyE,OAAO,EAAE;QACnB/R,QAAQ,EAAEiS,OAAO;QACjBhS,WAAW,EAAEiS,UAAU;QACvBvS,SAAS,EAAEwS,QAAQ;QACnBS,YAAY,EAAER,WAAW;QACzBS,gBAAgB,EAAER,eAAe;QACjCtT,qBAAqB,EAAEuT;OACxB,CAAC;IACJ;IAEA;IACA,IAAI,IAAI,CAACrL,MAAM,EAAE;MACf;MACA0E,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,eAAe,EAAE;MACxB,CAAC,EAAE,CAAC,CAAC;IACP;EACF;EAKA6F,sBAAsBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACxK,MAAM,EAAE;IAElB;IACA,IAAI,CAACwB,SAAS,CAACyD,OAAO,CAAC,CAAC4G,QAAQ,EAAEC,KAAK,KAAI;MACzC,IAAID,QAAQ,CAACnT,SAAS,EAAE;MAExB,MAAM2N,IAAI,GAAG,IAAI1G,IAAI,CAACkM,QAAQ,CAACxF,IAAI,CAAC;MACpC,MAAML,OAAO,GAAG,IAAI,CAACzG,UAAU,CAAC8G,IAAI,CAAC;MAErC;MACA,IAAI,IAAI,CAACtD,iBAAiB,CAACiD,OAAO,CAAC,EAAE;QACnC,MAAMsF,MAAM,GAAG,IAAI,CAACvI,iBAAiB,CAACiD,OAAO,CAAC;QAC9C,IAAI,CAACxE,SAAS,CAACsK,KAAK,CAAC,CAACH,YAAY,GAAGL,MAAM,CAACC,KAAK;QACjD,IAAI,CAAC/J,SAAS,CAACsK,KAAK,CAAC,CAACF,gBAAgB,GAAGN,MAAM,CAACjQ,SAAS;QACzD,IAAI,CAACmG,SAAS,CAACsK,KAAK,CAAC,CAAChU,qBAAqB,GAAGwT,MAAM,CAACC,KAAK,GAAG,CAAC,GAC1DC,IAAI,CAACC,KAAK,CAAEH,MAAM,CAACjQ,SAAS,GAAGiQ,MAAM,CAACC,KAAK,GAAI,GAAG,CAAC,GACnD,CAAC;QACL;MACF;MAEA;MACA,IAAI,IAAI,CAAC1J,UAAU,CAACmE,OAAO,CAAC,EAAE;QAC5B,MAAM+F,YAAY,GAAG,IAAI,CAAClK,UAAU,CAACmE,OAAO,CAAC;QAC7C,MAAMmF,WAAW,GAAGY,YAAY,CAACxL,MAAM;QACvC,MAAM6K,eAAe,GAAGW,YAAY,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC5Q,SAAS,CAAC,CAACkF,MAAM;QAEpE;QACA,IAAI,CAACwC,iBAAiB,CAACiD,OAAO,CAAC,GAAG;UAChCuF,KAAK,EAAEJ,WAAW;UAClB9P,SAAS,EAAE+P;SACZ;QAED;QACA,IAAI,CAAC5J,SAAS,CAACsK,KAAK,CAAC,CAACH,YAAY,GAAGR,WAAW;QAChD,IAAI,CAAC3J,SAAS,CAACsK,KAAK,CAAC,CAACF,gBAAgB,GAAGR,eAAe;QACxD,IAAI,CAAC5J,SAAS,CAACsK,KAAK,CAAC,CAAChU,qBAAqB,GAAGqT,WAAW,GAAG,CAAC,GACzDK,IAAI,CAACC,KAAK,CAAEL,eAAe,GAAGD,WAAW,GAAI,GAAG,CAAC,GACjD,CAAC;QACL;MACF;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAACxG,eAAe,EAAE;EACxB;EAEA;EACQqE,mBAAmBA,CAACpH,MAAe,EAAEyE,IAAU;IACrD,MAAM6F,OAAO,GAAG,IAAIvM,IAAI,CAAC0G,IAAI,CAAC;IAC9B,MAAM8F,SAAS,GAAGD,OAAO,CAACxB,MAAM,EAAE,CAAC,CAAC;IACpC;IACA,MAAM0B,eAAe,GAAGD,SAAS,KAAK,CAAC,GAAG,CAAC,GAAGA,SAAS,GAAG,CAAC,CAAC,CAAC;IAC7D,MAAME,UAAU,GAAGH,OAAO,CAACpB,OAAO,EAAE,CAAC,CAAC;IAEtCtH,OAAO,CAACC,GAAG,CAAC,wCAAwC,IAAI,CAAClE,UAAU,CAAC8G,IAAI,CAAC,kBAAkB8F,SAAS,aAAaC,eAAe,oBAAoBC,UAAU,EAAE,CAAC;IAEjK,MAAMtD,cAAc,GAAGnH,MAAM,CAACoK,MAAM,CAACxD,KAAK,IAAG;MAC3ChF,OAAO,CAACC,GAAG,CAAC,6BAA6B+E,KAAK,CAACnH,EAAE,KAAKmH,KAAK,CAAChN,IAAI,YAAYgN,KAAK,CAACvK,UAAU,aAAauK,KAAK,CAAC9J,WAAW,wBAAwB8J,KAAK,CAAC8D,iBAAiB,yBAAyB9D,KAAK,CAAC+D,kBAAkB,EAAE,CAAC;MAE7N,IAAI,CAAC/D,KAAK,CAACgE,MAAM,EAAE;QACjBhJ,OAAO,CAACC,GAAG,CAAC,oBAAoB+E,KAAK,CAACnH,EAAE,KAAKmH,KAAK,CAAChN,IAAI,gCAAgC,CAAC;QACxF,OAAO,KAAK;MACd;MAEA;MACA,IAAIgN,KAAK,CAACa,UAAU,EAAE;QACpB,MAAMoD,WAAW,GAAG,IAAI9M,IAAI,CAAC6I,KAAK,CAACa,UAAU,CAAC;QAC9CoD,WAAW,CAAC7M,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAChCsM,OAAO,CAACtM,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAE5B;QACA,IAAIsM,OAAO,GAAGO,WAAW,EAAE;UACzB,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAIjE,KAAK,CAAC9J,WAAW,KAAK,KAAK,EAAE;QAC/B,OAAO,IAAI;MACb;MAEA;MACA,IAAI8J,KAAK,CAAC9J,WAAW,KAAK,MAAM,EAAE;QAChC,IAAI,CAAC8J,KAAK,CAAC8D,iBAAiB,EAAE;UAC5B9I,OAAO,CAACC,GAAG,CAAC,oBAAoB+E,KAAK,CAACnH,EAAE,KAAKmH,KAAK,CAAChN,IAAI,yDAAyD,CAAC;UACjH,OAAO,IAAI,CAAC,CAAC;QACf;QAEA;QACA,IAAIkR,QAAQ,GAAU,EAAE;QACxB,IAAI,OAAOlE,KAAK,CAAC8D,iBAAiB,KAAK,QAAQ,EAAE;UAC/CI,QAAQ,GAAGlE,KAAK,CAAC8D,iBAAiB,CAACK,KAAK,CAAC,GAAG,CAAC,CAAC9V,GAAG,CAAC6U,GAAG,IAAIA,GAAG,CAACkB,IAAI,EAAE,CAAC;UACpEpJ,OAAO,CAACC,GAAG,CAAC,oBAAoB+E,KAAK,CAACnH,EAAE,KAAKmH,KAAK,CAAChN,IAAI,sCAAsCgN,KAAK,CAAC8D,iBAAiB,cAAc,EAAEI,QAAQ,CAAC;QAC/I,CAAC,MAAM,IAAIvK,KAAK,CAAC0K,OAAO,CAACrE,KAAK,CAAC8D,iBAAiB,CAAC,EAAE;UACjDI,QAAQ,GAAGlE,KAAK,CAAC8D,iBAAiB;UAClC9I,OAAO,CAACC,GAAG,CAAC,oBAAoB+E,KAAK,CAACnH,EAAE,KAAKmH,KAAK,CAAChN,IAAI,mCAAmC,EAAEkR,QAAQ,CAAC;QACvG;QAEA;QACA;QACA,MAAMI,YAAY,GAAG,IAAI,CAACC,eAAe,CAACX,eAAe,CAAC;QAC1D,MAAMY,WAAW,GAAG,IAAI,CAACC,cAAc,CAACb,eAAe,CAAC;QAExD5I,OAAO,CAACC,GAAG,CAAC,8BAA8BuJ,WAAW,KAAKF,YAAY,KAAKV,eAAe,oBAAoB,EAAEM,QAAQ,CAAC;QAEzH,MAAMQ,UAAU,GAAGR,QAAQ,CAACS,QAAQ,CAACf,eAAe,CAAC,IACnDM,QAAQ,CAACS,QAAQ,CAACf,eAAe,CAACtE,QAAQ,EAAE,CAAC,IAC7C4E,QAAQ,CAACS,QAAQ,CAACL,YAAY,CAAC,IAC/BJ,QAAQ,CAACS,QAAQ,CAACH,WAAW,CAAC;QAEhCxJ,OAAO,CAACC,GAAG,CAAC,oBAAoB+E,KAAK,CAACnH,EAAE,KAAKmH,KAAK,CAAChN,IAAI,wBAAwBwR,WAAW,KAAKE,UAAU,EAAE,CAAC;QAE5G,OAAOA,UAAU;MACnB;MAEA;MACA,IAAI1E,KAAK,CAAC9J,WAAW,KAAK,OAAO,EAAE;QACjC,IAAI,CAAC8J,KAAK,CAAC+D,kBAAkB,EAAE,OAAO,IAAI,CAAC,CAAC;QAE5C;QACA,IAAIG,QAAQ,GAAU,EAAE;QACxB,IAAI,OAAOlE,KAAK,CAAC+D,kBAAkB,KAAK,QAAQ,EAAE;UAChDG,QAAQ,GAAGlE,KAAK,CAAC+D,kBAAkB,CAACI,KAAK,CAAC,GAAG,CAAC,CAAC9V,GAAG,CAAC6U,GAAG,IAAI/H,QAAQ,CAAC+H,GAAG,CAACkB,IAAI,EAAE,CAAC,CAAC;QACjF,CAAC,MAAM,IAAIzK,KAAK,CAAC0K,OAAO,CAACrE,KAAK,CAAC+D,kBAAkB,CAAC,EAAE;UAClDG,QAAQ,GAAGlE,KAAK,CAAC+D,kBAAkB;QACrC;QAEA;QACA,OAAOG,QAAQ,CAACS,QAAQ,CAACd,UAAU,CAAC,IAClCK,QAAQ,CAACS,QAAQ,CAACd,UAAU,CAACvE,QAAQ,EAAE,CAAC;MAC5C;MAEA,OAAO,KAAK;IACd,CAAC,CAAC;IAEFtE,OAAO,CAACC,GAAG,CAAC,uBAAuB7B,MAAM,CAACrB,MAAM,cAAcwI,cAAc,CAACxI,MAAM,aAAa,IAAI,CAAChB,UAAU,CAAC8G,IAAI,CAAC,EAAE,CAAC;IAExH,OAAO0C,cAAc;EACvB;EAEApQ,UAAUA,CAACuT,OAAY;IACrB,IAAI,IAAI,CAACpK,aAAa,EAAE;MACtB;IACF;IAEA,MAAMsL,QAAQ,GAAGlB,OAAO,CAAC7F,IAAI;IAE7B,IAAI,CAACvE,aAAa,GAAG,IAAI;IAEzB,IAAI,CAACuL,gBAAgB,GAAGnB,OAAO;IAE/B,MAAM7F,IAAI,GAAG,IAAI1G,IAAI,CAACyN,QAAQ,CAAC;IAC/B,IAAI,CAACzR,YAAY,GAAG0K,IAAI;IAExB,IAAI,CAAC7E,SAAS,CAACyD,OAAO,CAAC4G,QAAQ,IAAG;MAChCA,QAAQ,CAAC7S,WAAW,GAAG6S,QAAQ,CAACxF,IAAI,KAAK+G,QAAQ;IACnD,CAAC,CAAC;IAEF,MAAME,aAAa,GAAG,IAAI,CAAC/N,UAAU,CAAC8G,IAAI,CAAC;IAE3C,IAAI,CAAC1D,MAAM,CAACyD,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;MAC/BhD,WAAW,EAAE;QACXiD,IAAI,EAAEiH,aAAa;QACnBhH,WAAW,EAAE,IAAI,CAAC3E,UAAU,KAAK,CAAC,GAAG,IAAI,CAACA,UAAU,GAAG;OACxD;MACD4E,UAAU,EAAE;KACb,CAAC;IAEF,IAAI,CAACzC,gBAAgB,EAAE;IAEvBY,UAAU,CAAC,MAAK;MACd,IAAI,CAACX,QAAQ,EAAE;MACf,IAAI,CAACjC,aAAa,GAAG,KAAK;IAC5B,CAAC,EAAE,EAAE,CAAC;EACR;EAKAyL,UAAUA,CAACC,SAAiB;IAC1B;IACA,IAAI,IAAI,CAACxK,cAAc,EAAE;MACvB;IACF;IAEA,IAAI,CAACA,cAAc,GAAG,IAAI;IAE1B;IACA,IAAI,CAACrB,UAAU,IAAI6L,SAAS;IAE5B;IACA,IAAI,CAAC3J,iBAAiB,EAAE;IAExB;IACA,IAAI,CAACc,eAAe,EAAE;IAEtB;IACA,MAAMrB,SAAS,GAAG,IAAI,CAAC/D,UAAU,CAAC,IAAI,CAAC5D,YAAY,CAAC;IACpD,IAAI,CAACgH,MAAM,CAACyD,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;MAC/BhD,WAAW,EAAE;QACXiD,IAAI,EAAE/C,SAAS;QACfgD,WAAW,EAAE,IAAI,CAAC3E;OACnB;MACD4E,UAAU,EAAE;KACb,CAAC;IAEF;IACA7B,UAAU,CAAC,MAAK;MACd,IAAI,CAAC1B,cAAc,GAAG,KAAK;IAC7B,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACQ2B,eAAeA,CAAA;IACrB,IAAI,CAAC,IAAI,CAAC3E,MAAM,EAAE;IAElB;IACA,IAAI,CAACwC,YAAY,CAAC8F,SAAS,CAAC,IAAI,CAACtI,MAAO,CAAC,CAACG,IAAI,CAC5CnJ,IAAI,CAAC,CAAC,CAAC,CACR,CAACoJ,SAAS,CAACqN,SAAS,IAAG;MACtB;MACA,MAAMC,eAAe,GAAG,IAAI,CAAClM,SAAS,CACnCwK,MAAM,CAACH,QAAQ,IAAI,CAACA,QAAQ,CAACnT,SAAS,CAAC,CACvC7B,GAAG,CAACgV,QAAQ,IAAG;QACd,MAAMxF,IAAI,GAAG,IAAI1G,IAAI,CAACkM,QAAQ,CAACxF,IAAI,CAAC;QACpC,MAAML,OAAO,GAAG,IAAI,CAACzG,UAAU,CAAC8G,IAAI,CAAC;QAErC;QACA,IAAI,IAAI,CAACtD,iBAAiB,CAACiD,OAAO,CAAC,EAAE;UACnC,OAAOlP,EAAE,CAAC;YACRuP,IAAI,EAAEwF,QAAQ,CAACxF,IAAI;YACnBqD,QAAQ,EAAE,IAAI,CAAC3G,iBAAiB,CAACiD,OAAO;WACzC,CAAC;QACJ;QAEA;QACA,MAAM2H,YAAY,GAAG,IAAI,CAAC3E,mBAAmB,CAACyE,SAAS,EAAEpH,IAAI,CAAC;QAE9D;QACA,IAAIsH,YAAY,CAACpN,MAAM,KAAK,CAAC,EAAE;UAC7B,MAAMqN,aAAa,GAAG;YAAErC,KAAK,EAAE,CAAC;YAAElQ,SAAS,EAAE;UAAC,CAAE;UAChD,IAAI,CAAC0H,iBAAiB,CAACiD,OAAO,CAAC,GAAG4H,aAAa;UAC/C,OAAO9W,EAAE,CAAC;YACRuP,IAAI,EAAEwF,QAAQ,CAACxF,IAAI;YACnBqD,QAAQ,EAAEkE;WACX,CAAC;QACJ;QAEA;QACA,OAAO,IAAI,CAACpL,YAAY,CAAC+G,uBAAuB,CAAC,IAAI,CAACvJ,MAAO,EAAEqG,IAAI,CAAC,CAAClG,IAAI,CACvEnJ,IAAI,CAAC,CAAC,CAAC,EACPH,GAAG,CAACgX,YAAY,IAAG;UACjB;UACA,MAAMC,QAAQ,GAAGH,YAAY,CAAC9W,GAAG,CAACoV,CAAC,IAAIA,CAAC,CAAC5K,EAAE,CAAC;UAC5C,MAAM0M,gBAAgB,GAAGF,YAAY,CAAC7B,MAAM,CAACgC,CAAC,IAAIF,QAAQ,CAACX,QAAQ,CAACa,CAAC,CAACrE,QAAQ,CAAC,CAAC;UAChF,MAAMyB,eAAe,GAAG2C,gBAAgB,CAAC/B,MAAM,CAACgC,CAAC,IAAIA,CAAC,CAAC3S,SAAS,CAAC,CAACkF,MAAM;UACxE,MAAM4K,WAAW,GAAGwC,YAAY,CAACpN,MAAM;UAEvC;UACA,MAAMmJ,QAAQ,GAAG;YACf6B,KAAK,EAAEJ,WAAW;YAClB9P,SAAS,EAAE+P;WACZ;UAED;UACA,IAAI,CAACrI,iBAAiB,CAACiD,OAAO,CAAC,GAAG0D,QAAQ;UAE1C,OAAO;YACLrD,IAAI,EAAEwF,QAAQ,CAACxF,IAAI;YACnBqD;WACD;QACH,CAAC,CAAC,CACH;MACH,CAAC,CAAC;MAEJ;MACA9S,QAAQ,CAAC8W,eAAe,CAAC,CAACtN,SAAS,CAAC6N,OAAO,IAAG;QAC5C;QACAA,OAAO,CAAChJ,OAAO,CAAC+E,MAAM,IAAG;UACvB,MAAM8B,KAAK,GAAG,IAAI,CAACtK,SAAS,CAAC0M,SAAS,CAACC,EAAE,IAAIA,EAAE,CAAC9H,IAAI,KAAK2D,MAAM,CAAC3D,IAAI,CAAC;UACrE,IAAIyF,KAAK,IAAI,CAAC,EAAE;YACd,IAAI,CAACtK,SAAS,CAACsK,KAAK,CAAC,CAACH,YAAY,GAAG3B,MAAM,CAACN,QAAQ,CAAC6B,KAAK;YAC1D,IAAI,CAAC/J,SAAS,CAACsK,KAAK,CAAC,CAACF,gBAAgB,GAAG5B,MAAM,CAACN,QAAQ,CAACrO,SAAS;YAClE,IAAI,CAACmG,SAAS,CAACsK,KAAK,CAAC,CAAChU,qBAAqB,GAAGkS,MAAM,CAACN,QAAQ,CAAC6B,KAAK,GAAG,CAAC,GACnEC,IAAI,CAACC,KAAK,CAAEzB,MAAM,CAACN,QAAQ,CAACrO,SAAS,GAAG2O,MAAM,CAACN,QAAQ,CAAC6B,KAAK,GAAI,GAAG,CAAC,GACrE,CAAC;UACP;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAzH,gBAAgBA,CAAA;IACd,MAAMpE,KAAK,GAAG,IAAIC,IAAI,EAAE;IACxB,IAAI,IAAI,CAACjE,SAAS,CAAC,IAAI,CAACC,YAAY,EAAE+D,KAAK,CAAC,EAAE;MAC5C,IAAI,CAACgC,UAAU,GAAG,OAAO;IAC3B,CAAC,MAAM,IAAI,IAAI,CAAChG,SAAS,CAAC,IAAI,CAACC,YAAY,EAAE,IAAIgE,IAAI,CAACD,KAAK,CAACmL,OAAO,CAACnL,KAAK,CAACoL,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;MAC1F,IAAI,CAACpJ,UAAU,GAAG,WAAW;IAC/B,CAAC,MAAM,IAAI,IAAI,CAAChG,SAAS,CAAC,IAAI,CAACC,YAAY,EAAE,IAAIgE,IAAI,CAACD,KAAK,CAACmL,OAAO,CAACnL,KAAK,CAACoL,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;MAC1F,IAAI,CAACpJ,UAAU,GAAG,UAAU;IAC9B,CAAC,MAAM;MACL;MACA,IAAI,CAACA,UAAU,GAAG,IAAI,CAAC/F,YAAY,CAACyS,kBAAkB,CAAC,OAAO,EAAE;QAC9DC,OAAO,EAAE,OAAO;QAChB3C,GAAG,EAAE,SAAS;QACd4C,KAAK,EAAE;OACR,CAAC;IACJ;EACF;EAKMrT,WAAWA,CAACuN,KAAmB;IAAA,IAAA+F,MAAA;IAAA,OAAA/I,iBAAA;MACnC,IAAI,CAAC+I,MAAI,CAACvO,MAAM,IAAI,CAACwI,KAAK,CAACnH,EAAE,EAAE;MAE/B;MACA,IAAIkN,MAAI,CAACtL,gBAAgB,CAACuF,KAAK,CAACnH,EAAE,CAAC,EAAE;QACnCmC,OAAO,CAACC,GAAG,CAAC,oBAAoB+E,KAAK,CAACnH,EAAE,KAAKmH,KAAK,CAAChN,IAAI,qDAAqD,CAAC;QAC7G;MACF;MAEA;MACA+S,MAAI,CAACtL,gBAAgB,CAACuF,KAAK,CAACnH,EAAE,CAAC,GAAG,IAAI;MACtCmC,OAAO,CAACC,GAAG,CAAC,wCAAwC+E,KAAK,CAACnH,EAAE,KAAKmH,KAAK,CAAChN,IAAI,GAAG,CAAC;MAE/E,IAAI;QACF;QACA;QACA;QAEA;QACAgI,OAAO,CAACC,GAAG,CAAC,oBAAoB+E,KAAK,CAACnH,EAAE,KAAKmH,KAAK,CAAChN,IAAI,qCAAqCgN,KAAK,CAAC5O,cAAc,EAAE,CAAC;QAEnH;QACA;QACA,OAAO2U,MAAI,CAACtL,gBAAgB,CAACuF,KAAK,CAACnH,EAAE,CAAC;QACtC;MACF,CAAC,CAAC,OAAOH,KAAK,EAAE;QACdsC,OAAO,CAACtC,KAAK,CAAC,uCAAuCsH,KAAK,CAACnH,EAAE,KAAKmH,KAAK,CAAChN,IAAI,IAAI,EAAE0F,KAAK,CAAC;MAC1F,CAAC,SAAS;QACR;QACA,OAAOqN,MAAI,CAACtL,gBAAgB,CAACuF,KAAK,CAACnH,EAAE,CAAC;QACtCmC,OAAO,CAACC,GAAG,CAAC,wCAAwC+E,KAAK,CAACnH,EAAE,KAAKmH,KAAK,CAAChN,IAAI,GAAG,CAAC;MACjF;IAAC;EACH;EAKM1B,mBAAmBA,CAAC0O,KAAmB,EAAEgG,KAAW;IAAA,IAAAC,MAAA;IAAA,OAAAjJ,iBAAA;MACxD,IAAI,CAACiJ,MAAI,CAACzO,MAAM,IAAI,CAACwI,KAAK,CAACnH,EAAE,EAAE;MAE/B;MACA,IAAIoN,MAAI,CAACvL,gBAAgB,CAACsF,KAAK,CAACnH,EAAE,CAAC,EAAE;QACnC;MACF;MAEA;MACAoN,MAAI,CAACvL,gBAAgB,CAACsF,KAAK,CAACnH,EAAE,CAAC,GAAG,IAAI;MAEtC,IAAI;QACF;QACA,MAAMqN,kBAAkB,GAAGlG,KAAK,CAACnN,SAAS;QAC1CmI,OAAO,CAACC,GAAG,CAAC,oBAAoB+E,KAAK,CAACnH,EAAE,KAAKmH,KAAK,CAAChN,IAAI,+BAA+BkT,kBAAkB,EAAE,CAAC;QAE3G;QACA,IAAIF,KAAK,EAAE;UACT;UACA,MAAM7H,MAAM,GAAG6H,KAAK,CAACxU,MAAM,KAAKwU,KAAK,CAACG,MAAM,GAAGH,KAAK,CAACG,MAAM,CAAC9R,KAAK,GAAG,IAAI,CAAC;UACzE4R,MAAI,CAACxU,sBAAsB,CAAC0M,MAAM,CAAC;UAEnC;UACA,MAAME,aAAa,GAAGF,MAAM,YAAYa,WAAW,GAAGb,MAAM,CAACG,YAAY,CAAC,eAAe,CAAC,GAAG,IAAI;UACjG,IAAID,aAAa,IAAIA,aAAa,KAAK2B,KAAK,CAACnH,EAAE,EAAE;YAC/C,OAAOoN,MAAI,CAACvL,gBAAgB,CAACsF,KAAK,CAACnH,EAAE,CAAC;YACtC;UACF;UAEA;UACA,IAAI0F,WAAW,GAAG,CAAC;UACnB,IAAIyH,KAAK,CAACG,MAAM,IAAIH,KAAK,CAACG,MAAM,CAAC9R,KAAK,KAAK6G,SAAS,EAAE;YACpD;YACAqD,WAAW,GAAGyH,KAAK,CAACG,MAAM,CAAC9R,KAAK;UAClC,CAAC,MAAM,IAAI8J,MAAM,YAAYC,gBAAgB,EAAE;YAC7C;YACAG,WAAW,GAAGpD,QAAQ,CAACgD,MAAM,CAAC9J,KAAK,CAAC;UACtC,CAAC,MAAM,IAAI8J,MAAM,YAAYa,WAAW,IAAIb,MAAM,CAACc,OAAO,KAAK,WAAW,EAAE;YAC1E;YACA,MAAMC,SAAS,GAAGf,MAAM,CAACG,YAAY,CAAC,OAAO,CAAC,IAAI,GAAG;YACrDC,WAAW,GAAGpD,QAAQ,CAAC+D,SAAS,CAAC;UACnC;UAEA;UACAc,KAAK,CAAC5O,cAAc,GAAGmN,WAAW;UAElC;UACA;UACA,IAAIyB,KAAK,CAACvK,UAAU,KAAK,OAAO,EAAE;YAChC;YACAuK,KAAK,CAACnN,SAAS,GAAG0L,WAAW,IAAIyB,KAAK,CAACrO,UAAU;UACnD,CAAC,MAAM;YAAE;YACP;YACAqO,KAAK,CAACnN,SAAS,GAAG0L,WAAW,GAAGyB,KAAK,CAACrO,UAAU;UAClD;UAEAqJ,OAAO,CAACC,GAAG,CAAC,oBAAoB+E,KAAK,CAACnH,EAAE,KAAKmH,KAAK,CAAChN,IAAI,0BAA0BgN,KAAK,CAACnN,SAAS,EAAE,CAAC;QACrG;QAEA;QACA,MAAMuT,SAAS,GAAG;UAAE,GAAGpG;QAAK,CAAE;QAE9B;QACA,MAAMwB,MAAM,SAASyE,MAAI,CAACjM,YAAY,CAACqM,qBAAqB,CAC1DJ,MAAI,CAACzO,MAAM,EACXwI,KAAK,CAACnH,EAAE,EACRoN,MAAI,CAAC9S,YAAY,EACjB6M,KAAK,CAAC5O,cAAc,EACpBgV,SAAS,CACV;QAED;QACApG,KAAK,CAACnN,SAAS,GAAG2O,MAAM,CAAC3O,SAAS;QAClCmN,KAAK,CAAC5O,cAAc,GAAGoQ,MAAM,CAACpQ,cAAc;QAE5C;QACA,MAAM8F,KAAK,GAAG,IAAIC,IAAI,EAAE;QACxBD,KAAK,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC1B,MAAMjE,YAAY,GAAG,IAAIgE,IAAI,CAAC8O,MAAI,CAAC9S,YAAY,CAAC;QAChDA,YAAY,CAACiE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACjC,MAAMC,eAAe,GAAGlE,YAAY,CAACmE,OAAO,EAAE,KAAKJ,KAAK,CAACI,OAAO,EAAE;QAElE;QACA,IAAID,eAAe,EAAE;UACnB;UACA;UAEA;UACA,IAAI/E,MAAM,GAAGkP,MAAM,CAAClP,MAAM;UAE1B;UACA,MAAMgU,cAAc,GAAGtG,KAAK,CAACnN,SAAS;UAEtCmI,OAAO,CAACC,GAAG,CAAC,oBAAoB+E,KAAK,CAACnH,EAAE,KAAKmH,KAAK,CAAChN,IAAI,4BAA4BkT,kBAAkB,SAASI,cAAc,EAAE,CAAC;UAE/H;UACA,IAAIJ,kBAAkB,KAAKI,cAAc,EAAE;YACzC,IAAIA,cAAc,EAAE;cAClB;cACAhU,MAAM,EAAE;cACR0I,OAAO,CAACC,GAAG,CAAC,oBAAoB+E,KAAK,CAACnH,EAAE,KAAKmH,KAAK,CAAChN,IAAI,8DAA8DV,MAAM,EAAE,CAAC;YAChI,CAAC,MAAM;cACL;cACAA,MAAM,GAAG0Q,IAAI,CAACrE,GAAG,CAAC,CAAC,EAAErM,MAAM,GAAG,CAAC,CAAC;cAChC0I,OAAO,CAACC,GAAG,CAAC,oBAAoB+E,KAAK,CAACnH,EAAE,KAAKmH,KAAK,CAAChN,IAAI,8DAA8DV,MAAM,EAAE,CAAC;YAChI;YAEA;YACA2T,MAAI,CAACjM,YAAY,CAACuH,iBAAiB,CAACvB,KAAK,CAACnH,EAAG,EAAEvG,MAAM,CAAC,CAACsF,SAAS,CAAC;cAC/DC,IAAI,EAAEA,CAAA,KAAK;gBACTmD,OAAO,CAACC,GAAG,CAAC,oDAAoD+E,KAAK,CAACnH,EAAE,OAAOvG,MAAM,EAAE,CAAC;gBAExF;gBACA,MAAMkL,OAAO,GAAGyI,MAAI,CAAClP,UAAU,CAACkP,MAAI,CAAC9S,YAAY,CAAC;gBAClD,IAAI8S,MAAI,CAAC5M,UAAU,CAACmE,OAAO,CAAC,EAAE;kBAC5B,MAAM+I,gBAAgB,GAAGN,MAAI,CAAC5M,UAAU,CAACmE,OAAO,CAAC,CAACkI,SAAS,CAACjC,CAAC,IAAIA,CAAC,CAAC5K,EAAE,KAAKmH,KAAK,CAACnH,EAAE,CAAC;kBACnF,IAAI0N,gBAAgB,IAAI,CAAC,EAAE;oBACzBN,MAAI,CAAC5M,UAAU,CAACmE,OAAO,CAAC,CAAC+I,gBAAgB,CAAC,CAACjU,MAAM,GAAGA,MAAM;kBAC5D;gBACF;cACF,CAAC;cACDoG,KAAK,EAAGA,KAAU,IAAKsC,OAAO,CAACtC,KAAK,CAAC,8CAA8CsH,KAAK,CAACnH,EAAE,GAAG,EAAEH,KAAK;aACtG,CAAC;UACJ,CAAC,MAAM;YACLsC,OAAO,CAACC,GAAG,CAAC,oBAAoB+E,KAAK,CAACnH,EAAE,KAAKmH,KAAK,CAAChN,IAAI,yDAAyDV,MAAM,EAAE,CAAC;UAC3H;QACF,CAAC,MAAM;UACL;UACA0I,OAAO,CAACC,GAAG,CAAC,6CAA6CgL,MAAI,CAAClP,UAAU,CAACkP,MAAI,CAAC9S,YAAY,CAAC,mCAAmC,CAAC;UAE/H;UACA8S,MAAI,CAACjM,YAAY,CAACwM,QAAQ,CAACxG,KAAK,CAACnH,EAAG,CAAC,CAACjB,SAAS,CAACe,YAAY,IAAG;YAC7D,IAAI,CAACA,YAAY,EAAE;cACjBqC,OAAO,CAACtC,KAAK,CAAC,8CAA8CsH,KAAK,CAACnH,EAAE,EAAE,CAAC;cACvE;YACF;YAEA;YACAoN,MAAI,CAAC5L,gBAAgB,CAACoM,eAAe,CAACR,MAAI,CAACzO,MAAO,EAAEwI,KAAK,CAACnH,EAAG,CAAC,CAC3DL,IAAI,CAAC8I,gBAAgB,IAAG;cACvBtG,OAAO,CAACC,GAAG,CAAC,4CAA4C+E,KAAK,CAACnH,EAAE,eAAeyI,gBAAgB,EAAE,CAAC;cAElG;cACA2E,MAAI,CAACjM,YAAY,CAACuH,iBAAiB,CAACvB,KAAK,CAACnH,EAAG,EAAEyI,gBAAgB,CAAC,CAAC1J,SAAS,CAAC;gBACzEC,IAAI,EAAEA,CAAA,KAAK;kBACTmD,OAAO,CAACC,GAAG,CAAC,oDAAoD+E,KAAK,CAACnH,EAAE,OAAOyI,gBAAgB,EAAE,CAAC;kBAElG;kBACA,MAAMoF,WAAW,GAAGT,MAAI,CAAClP,UAAU,CAACG,KAAK,CAAC;kBAC1C8D,OAAO,CAACC,GAAG,CAAC,gGAAgG,CAAC;kBAC7G,OAAOgL,MAAI,CAAC5M,UAAU,CAACqN,WAAW,CAAC;kBAEnC;kBACA,MAAMC,UAAU,GAAGV,MAAI,CAACjN,SAAS,CAAC0M,SAAS,CAACC,EAAE,IAAIA,EAAE,CAAC9H,IAAI,KAAK6I,WAAW,CAAC;kBAC1E,IAAIC,UAAU,IAAI,CAAC,EAAE;oBACnB,OAAOV,MAAI,CAAC1L,iBAAiB,CAACmM,WAAW,CAAC;oBAC1CT,MAAI,CAACW,yBAAyB,CAACF,WAAW,CAAC;kBAC7C;gBACF,CAAC;gBACDhO,KAAK,EAAGA,KAAU,IAAKsC,OAAO,CAACtC,KAAK,CAAC,8CAA8CsH,KAAK,CAACnH,EAAE,GAAG,EAAEH,KAAK;eACtG,CAAC;YACJ,CAAC,CAAC,CACDgJ,KAAK,CAAChJ,KAAK,IAAG;cACbsC,OAAO,CAACtC,KAAK,CAAC,iDAAiDsH,KAAK,CAACnH,EAAE,GAAG,EAAEH,KAAK,CAAC;YACpF,CAAC,CAAC;UACN,CAAC,CAAC;QACJ;QAEA;QACAuN,MAAI,CAACY,aAAa,CAAC7G,KAAK,CAAC;QAEzB;QACA,MAAMxC,OAAO,GAAGyI,MAAI,CAAClP,UAAU,CAACkP,MAAI,CAAC9S,YAAY,CAAC;QAClD,IAAI8S,MAAI,CAAC5M,UAAU,CAACmE,OAAO,CAAC,EAAE;UAC5B;UACA,MAAM+I,gBAAgB,GAAGN,MAAI,CAAC5M,UAAU,CAACmE,OAAO,CAAC,CAACkI,SAAS,CAACjC,CAAC,IAAIA,CAAC,CAAC5K,EAAE,KAAKmH,KAAK,CAACnH,EAAE,CAAC;UACnF,IAAI0N,gBAAgB,IAAI,CAAC,EAAE;YACzBN,MAAI,CAAC5M,UAAU,CAACmE,OAAO,CAAC,CAAC+I,gBAAgB,CAAC,GAAG;cAAE,GAAGvG;YAAK,CAAE;UAC3D;QACF;QAEA;QACA,OAAOiG,MAAI,CAAC1L,iBAAiB,CAACiD,OAAO,CAAC;QAEtC;QACAyI,MAAI,CAACW,yBAAyB,CAACpJ,OAAO,CAAC;QAEvC;QACAyI,MAAI,CAAClE,uBAAuB,CAACkE,MAAI,CAAC7M,MAAM,CAAC;MAC3C,CAAC,CAAC,OAAOV,KAAK,EAAE;QACdsC,OAAO,CAACtC,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACnE,CAAC,SAAS;QACR;QACA,OAAOuN,MAAI,CAACvL,gBAAgB,CAACsF,KAAK,CAACnH,EAAE,CAAC;MACxC;IAAC;EACH;EAEA;EACQ+N,yBAAyBA,CAACpJ,OAAe;IAC/C;IACA,MAAM8F,KAAK,GAAG,IAAI,CAACtK,SAAS,CAAC0M,SAAS,CAACC,EAAE,IAAIA,EAAE,CAAC9H,IAAI,KAAKL,OAAO,CAAC;IACjE,IAAI8F,KAAK,GAAG,CAAC,EAAE;IAEf;IACA,IAAI,IAAI,CAACjK,UAAU,CAACmE,OAAO,CAAC,EAAE;MAC5B,MAAM+F,YAAY,GAAG,IAAI,CAAClK,UAAU,CAACmE,OAAO,CAAC;MAC7C,MAAMmF,WAAW,GAAGY,YAAY,CAACxL,MAAM;MACvC,MAAM6K,eAAe,GAAGW,YAAY,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC5Q,SAAS,CAAC,CAACkF,MAAM;MAEpE;MACA,IAAI,CAACwC,iBAAiB,CAACiD,OAAO,CAAC,GAAG;QAChCuF,KAAK,EAAEJ,WAAW;QAClB9P,SAAS,EAAE+P;OACZ;MAED;MACA,IAAI,CAAC5J,SAAS,CAACsK,KAAK,CAAC,CAACH,YAAY,GAAGR,WAAW;MAChD,IAAI,CAAC3J,SAAS,CAACsK,KAAK,CAAC,CAACF,gBAAgB,GAAGR,eAAe;MACxD,IAAI,CAAC5J,SAAS,CAACsK,KAAK,CAAC,CAAChU,qBAAqB,GAAGqT,WAAW,GAAG,CAAC,GACzDK,IAAI,CAACC,KAAK,CAAEL,eAAe,GAAGD,WAAW,GAAI,GAAG,CAAC,GACjD,CAAC;MAEL;IACF;IAEA;IACA,IAAI,IAAI,CAACnL,MAAM,EAAE;MACf,MAAMqG,IAAI,GAAG,IAAI1G,IAAI,CAACqG,OAAO,CAAC;MAE9B,IAAI,CAACxD,YAAY,CAAC+G,uBAAuB,CAAC,IAAI,CAACvJ,MAAM,EAAEqG,IAAI,CAAC,CAAClG,IAAI,CAC/DnJ,IAAI,CAAC,CAAC,CAAC,CACR,CAACoJ,SAAS,CAACyN,YAAY,IAAG;QACzB,IAAI,CAACrL,YAAY,CAAC8F,SAAS,CAAC,IAAI,CAACtI,MAAO,CAAC,CAACG,IAAI,CAC5CnJ,IAAI,CAAC,CAAC,CAAC,CACR,CAACoJ,SAAS,CAACwB,MAAM,IAAG;UACnB;UACA,MAAM+L,YAAY,GAAG,IAAI,CAAC3E,mBAAmB,CAACpH,MAAM,EAAEyE,IAAI,CAAC;UAE3D;UACA,MAAMyH,QAAQ,GAAGH,YAAY,CAAC9W,GAAG,CAACoV,CAAC,IAAIA,CAAC,CAAC5K,EAAE,CAAC;UAC5C,MAAM0M,gBAAgB,GAAGF,YAAY,CAAC7B,MAAM,CAACgC,CAAC,IAAIF,QAAQ,CAACX,QAAQ,CAACa,CAAC,CAACrE,QAAQ,CAAC,CAAC;UAChF,MAAMyB,eAAe,GAAG2C,gBAAgB,CAAC/B,MAAM,CAACgC,CAAC,IAAIA,CAAC,CAAC3S,SAAS,CAAC,CAACkF,MAAM;UACxE,MAAM4K,WAAW,GAAGwC,YAAY,CAACpN,MAAM;UAEvC;UACA,IAAI,CAACwC,iBAAiB,CAACiD,OAAO,CAAC,GAAG;YAChCuF,KAAK,EAAEJ,WAAW;YAClB9P,SAAS,EAAE+P;WACZ;UAED;UACA,IAAI,CAAC5J,SAAS,CAACsK,KAAK,CAAC,CAACH,YAAY,GAAGR,WAAW;UAChD,IAAI,CAAC3J,SAAS,CAACsK,KAAK,CAAC,CAACF,gBAAgB,GAAGR,eAAe;UACxD,IAAI,CAAC5J,SAAS,CAACsK,KAAK,CAAC,CAAChU,qBAAqB,GAAGqT,WAAW,GAAG,CAAC,GACzDK,IAAI,CAACC,KAAK,CAAEL,eAAe,GAAGD,WAAW,GAAI,GAAG,CAAC,GACjD,CAAC;QACP,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF;EAEA;EACQkE,aAAaA,CAAC7G,KAAmB;IACvC;IACA,MAAM8G,YAAY,GAAG7I,QAAQ,CAAC8I,aAAa,CAAC,mBAAmB/G,KAAK,CAACnH,EAAE,IAAI,CAAC;IAC5E,IAAI,CAACiO,YAAY,EAAE;MACjB9L,OAAO,CAACtC,KAAK,CAAC,mDAAmDsH,KAAK,CAACnH,EAAE,EAAE,CAAC;MAC5E;IACF;IAEA;IACA,IAAImH,KAAK,CAACnN,SAAS,EAAE;MACnBiU,YAAY,CAACE,SAAS,CAAChL,GAAG,CAAC,WAAW,CAAC;IACzC,CAAC,MAAM;MACL8K,YAAY,CAACE,SAAS,CAACC,MAAM,CAAC,WAAW,CAAC;IAC5C;IAEA;IACA,MAAMC,cAAc,GAAGJ,YAAY,CAAC5I,gBAAgB,CAAC,eAAe,CAAC;IACrE,IAAIgJ,cAAc,IAAIA,cAAc,CAACnP,MAAM,GAAG,CAAC,EAAE;MAC/C;MACA,MAAMb,KAAK,GAAG,IAAIC,IAAI,EAAE;MACxBD,KAAK,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC1B,MAAMjE,YAAY,GAAG,IAAIgE,IAAI,CAAC,IAAI,CAAChE,YAAY,CAAC;MAChDA,YAAY,CAACiE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACjC,MAAMC,eAAe,GAAGlE,YAAY,CAACmE,OAAO,EAAE,KAAKJ,KAAK,CAACI,OAAO,EAAE;MAElE;MACA,IAAID,eAAe,EAAE;QACnB,MAAM8P,WAAW,GAAGnH,KAAK,CAAC1N,MAAM,IAAI,CAAC;QACrC0I,OAAO,CAACC,GAAG,CAAC,oBAAoB+E,KAAK,CAACnH,EAAE,gBAAgBmH,KAAK,CAACnN,SAAS,aAAasU,WAAW,EAAE,CAAC;QAElG;QACAD,cAAc,CAACzK,OAAO,CAAC2K,OAAO,IAAG;UAC/B,IAAIA,OAAO,CAACC,aAAa,IAAID,OAAO,CAACC,aAAa,CAACC,QAAQ,CAACF,OAAO,CAAC,EAAE;YACpE;YACCA,OAAuB,CAACvI,KAAK,CAAC0I,OAAO,GAAG,OAAO;YAChDH,OAAO,CAACI,WAAW,GAAG,KAAKL,WAAW,GAAG;UAC3C;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACAD,cAAc,CAACzK,OAAO,CAAC2K,OAAO,IAAG;UAC/B,IAAIA,OAAO,CAACC,aAAa,IAAID,OAAO,CAACC,aAAa,CAACC,QAAQ,CAACF,OAAO,CAAC,EAAE;YACnEA,OAAuB,CAACvI,KAAK,CAAC0I,OAAO,GAAG,MAAM;YAC/CH,OAAO,CAACI,WAAW,GAAG,EAAE;UAC1B;QACF,CAAC,CAAC;MACJ;IACF;IAEA;IACA,MAAMC,YAAY,GAAGX,YAAY,CAACC,aAAa,CAAC,gBAAgB,CAAC;IACjE,IAAIU,YAAY,EAAE;MAAA,IAAAC,qBAAA;MAChB,MAAMC,UAAU,IAAAD,qBAAA,GAAGD,YAAY,CAACJ,aAAa,cAAAK,qBAAA,uBAA1BA,qBAAA,CAA4BV,SAAS,CAACM,QAAQ,CAAC,eAAe,CAAC;MAClF,MAAMM,UAAU,GAAGD,UAAU,GAAG,GAAG,GAAG,EAAE;MACxC,MAAME,cAAc,GAAG7H,KAAK,CAAClO,SAAS,KAAK,OAAO,IAAI,CAAC6V,UAAU,GAAG,IAAI3H,KAAK,CAAClO,SAAS,EAAE,GAAG,EAAE;MAE9F2V,YAAY,CAACD,WAAW,GAAG,GAAGxH,KAAK,CAAC5O,cAAc,GAAGwW,UAAU,IAAI5H,KAAK,CAACrO,UAAU,GAAGiW,UAAU,GAAGC,cAAc,EAAE;IACrH;IAEA7M,OAAO,CAACC,GAAG,CAAC,mCAAmC+E,KAAK,CAACnH,EAAE,EAAE,CAAC;EAC5D;EAEA;EACApH,sBAAsBA,CAAC0M,MAA6C;IAClE,IAAI,CAACA,MAAM,EAAE;MACX;IACF;IAEA;IACA,IAAI2J,aAA0B;IAC9B,IAAIvJ,WAAW,GAAG,CAAC;IACnB,IAAIC,QAAQ,GAAG,CAAC;IAChB,IAAIE,QAAQ,GAAG,GAAG;IAClB,IAAIL,aAAa,GAAG,EAAE;IAEtB,IAAIF,MAAM,YAAYC,gBAAgB,EAAE;MACtC;MACA0J,aAAa,GAAG3J,MAAM;MACtBE,aAAa,GAAGF,MAAM,CAACG,YAAY,CAAC,eAAe,CAAC,IAAI,EAAE;MAC1DC,WAAW,GAAGpD,QAAQ,CAACgD,MAAM,CAAC9J,KAAK,CAAC;MACpCmK,QAAQ,GAAGrD,QAAQ,CAACgD,MAAM,CAACM,GAAG,CAAC;MAC/BC,QAAQ,GAAGvD,QAAQ,CAACgD,MAAM,CAACQ,GAAG,CAAC;IACjC,CAAC,MAAM,IAAIR,MAAM,YAAYa,WAAW,IAAIb,MAAM,CAACc,OAAO,KAAK,WAAW,EAAE;MAC1E;MACA6I,aAAa,GAAG3J,MAAM;MACtBE,aAAa,GAAGF,MAAM,CAACG,YAAY,CAAC,eAAe,CAAC,IAAI,EAAE;MAE1D;MACA,MAAMY,SAAS,GAAGf,MAAM,CAACG,YAAY,CAAC,OAAO,CAAC,IAAI,GAAG;MACrD,MAAMa,OAAO,GAAGhB,MAAM,CAACG,YAAY,CAAC,KAAK,CAAC,IAAI,GAAG;MACjD,MAAMc,OAAO,GAAGjB,MAAM,CAACG,YAAY,CAAC,KAAK,CAAC,IAAI,KAAK;MAEnDC,WAAW,GAAGpD,QAAQ,CAAC+D,SAAS,CAAC;MACjCV,QAAQ,GAAGrD,QAAQ,CAACgE,OAAO,CAAC;MAC5BT,QAAQ,GAAGvD,QAAQ,CAACiE,OAAO,CAAC;IAC9B,CAAC,MAAM;MACL;IACF;IAEA,IAAI,CAACf,aAAa,EAAE;MAClB;IACF;IAEA;IACA,MAAMO,UAAU,GAAGF,QAAQ,GAAGF,QAAQ,GACnC,CAACD,WAAW,GAAGC,QAAQ,KAAKE,QAAQ,GAAGF,QAAQ,CAAC,GAAI,GAAG,GAAG,CAAC;IAE9D;IACA,IAAIsJ,aAAa,CAAC7I,OAAO,KAAK,WAAW,EAAE;MACzC6I,aAAa,CAACjJ,KAAK,CAACQ,WAAW,CAAC,kBAAkB,EAAE,GAAGT,UAAU,GAAG,CAAC;IACvE,CAAC,MAAM;MACL;MACAkJ,aAAa,CAACjJ,KAAK,CAACC,UAAU,GAC5B,iDAAiDF,UAAU,cAAcA,UAAU,kBAAkB;IACzG;IAEA;IACAkJ,aAAa,CAAC/I,YAAY,CAAC,oBAAoB,EAAER,WAAW,CAACe,QAAQ,EAAE,CAAC;EAC1E;EAKA;;;;;EAKM9L,eAAeA,CAACwE,SAAqB;IAAA,IAAA+P,MAAA;IAAA,OAAA/K,iBAAA;MACzC,IAAI,CAAC+K,MAAI,CAACvQ,MAAM,IAAI,CAACQ,SAAS,CAACa,EAAE,EAAE;MAEnC;MACA,IAAIkP,MAAI,CAACpN,oBAAoB,CAAC3C,SAAS,CAACa,EAAE,CAAC,EAAE;QAC3CmC,OAAO,CAACC,GAAG,CAAC,yBAAyBjD,SAAS,CAACa,EAAE,oDAAoD,CAAC;QACtG;MACF;MAEA;MACAkP,MAAI,CAACpN,oBAAoB,CAAC3C,SAAS,CAACa,EAAE,CAAC,GAAG,IAAI;MAC9CmC,OAAO,CAACC,GAAG,CAAC,6CAA6CjD,SAAS,CAACa,EAAE,EAAE,CAAC;MAExE,IAAI;QACF;QACA;QACAmC,OAAO,CAACC,GAAG,CAAC,wCAAwCjD,SAAS,CAACa,EAAE,EAAE,CAAC;QAEnE;QACA,MAAMmP,QAAQ,GAAGhQ,SAAS,CAAC5G,cAAc,KAAK,CAAC,GAAG4G,SAAS,CAACtE,aAAa,CAAC/B,UAAU,GAAG,CAAC;QACxF,MAAMsW,iBAAiB,GAAGD,QAAQ,KAAKhQ,SAAS,CAACtE,aAAa,CAAC/B,UAAU;QAEzE;QACAqG,SAAS,CAAC5G,cAAc,GAAG4W,QAAQ;QACnChQ,SAAS,CAACnF,SAAS,GAAGoV,iBAAiB;QAEvCjN,OAAO,CAACC,GAAG,CAAC,iCAAiCjD,SAAS,CAACa,EAAE,aAAab,SAAS,CAAC5G,cAAc,gBAAgB4G,SAAS,CAACnF,SAAS,EAAE,CAAC;QAEpI;QACA,MAAMqE,KAAK,GAAG,IAAIC,IAAI,EAAE;QACxBD,KAAK,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC1B,MAAMjE,YAAY,GAAG,IAAIgE,IAAI,CAAC4Q,MAAI,CAAC5U,YAAY,CAAC;QAChDA,YAAY,CAACiE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACjC,MAAMoL,OAAO,GAAGrP,YAAY,CAACmE,OAAO,EAAE,KAAKJ,KAAK,CAACI,OAAO,EAAE;QAE1D;QACA,IAAI,CAACkL,OAAO,EAAE;UACZxH,OAAO,CAACC,GAAG,CAAC,sDAAsD8M,MAAI,CAAChR,UAAU,CAACgR,MAAI,CAAC5U,YAAY,CAAC,EAAE,CAAC;UACvG,OAAO4U,MAAI,CAACpN,oBAAoB,CAAC3C,SAAS,CAACa,EAAE,CAAC;UAC9C;QACF;QAEA;QACAkP,MAAI,CAACG,iBAAiB,CAAClQ,SAAS,CAAC;QAEjC,IAAI;UACF,MAAMwJ,MAAM,SAASuG,MAAI,CAACtQ,gBAAgB,CAAC0Q,yBAAyB,CAClEnQ,SAAS,CAACa,EAAE,EACZkP,MAAI,CAACvQ,MAAM,EACXuQ,MAAI,CAAC5U,YAAY,CAAC;WACnB;UAED6H,OAAO,CAACC,GAAG,CAAC,8CAA8CjD,SAAS,CAACa,EAAE,EAAE,CAAC;UACzEmC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEuG,MAAM,CAAC;UAEjD;UACAxJ,SAAS,CAACnF,SAAS,GAAG2O,MAAM,CAAC3O,SAAS;UACtCmF,SAAS,CAAC5G,cAAc,GAAGoQ,MAAM,CAACpQ,cAAc;UAChD4G,SAAS,CAAC1F,MAAM,GAAGkP,MAAM,CAAClP,MAAM;UAEhC;UACAyV,MAAI,CAACG,iBAAiB,CAAClQ,SAAS,CAAC;UAEjC;UACA;UACA,MAAMwF,OAAO,GAAGuK,MAAI,CAAChR,UAAU,CAACgR,MAAI,CAAC5U,YAAY,CAAC;UAClD,OAAO4U,MAAI,CAACxN,iBAAiB,CAACiD,OAAO,CAAC;UAEtC;UACAuK,MAAI,CAACnB,yBAAyB,CAACpJ,OAAO,CAAC;UAEvC;UACAuK,MAAI,CAAChG,uBAAuB,CAACgG,MAAI,CAAC3O,MAAM,CAAC;UAEzC;UACA,OAAO2O,MAAI,CAACpN,oBAAoB,CAAC3C,SAAS,CAACa,EAAE,CAAC;UAC9CmC,OAAO,CAACC,GAAG,CAAC,6CAA6CjD,SAAS,CAACa,EAAE,EAAE,CAAC;QAC1E,CAAC,CAAC,OAAOH,KAAK,EAAE;UACdsC,OAAO,CAACtC,KAAK,CAAC,wCAAwCV,SAAS,CAACa,EAAE,GAAG,EAAEH,KAAK,CAAC;UAE7E;UACAV,SAAS,CAAC5G,cAAc,GAAG4G,SAAS,CAAC5G,cAAc,KAAK,CAAC,GAAG4G,SAAS,CAACtE,aAAa,CAAC/B,UAAU,GAAG,CAAC;UAClGqG,SAAS,CAACnF,SAAS,GAAGmF,SAAS,CAAC5G,cAAc,KAAK4G,SAAS,CAACtE,aAAa,CAAC/B,UAAU;UACrFoW,MAAI,CAACG,iBAAiB,CAAClQ,SAAS,CAAC;UAEjC,OAAO+P,MAAI,CAACpN,oBAAoB,CAAC3C,SAAS,CAACa,EAAE,CAAC;QAChD;MACF,CAAC,CAAC,OAAOH,KAAK,EAAE;QACdsC,OAAO,CAACtC,KAAK,CAAC,2CAA2CV,SAAS,CAACa,EAAE,GAAG,EAAEH,KAAK,CAAC;QAChF,OAAOqP,MAAI,CAACpN,oBAAoB,CAAC3C,SAAS,CAACa,EAAE,CAAC;MAChD;IAAC;EACH;EAEA;EACQqP,iBAAiBA,CAAClQ,SAAqB;IAC7C;IACA,MAAM8O,YAAY,GAAG7I,QAAQ,CAAC8I,aAAa,CAAC,qCAAqC/O,SAAS,CAACa,EAAE,IAAI,CAAC;IAClG,IAAI,CAACiO,YAAY,EAAE;MACjB9L,OAAO,CAACtC,KAAK,CAAC,wDAAwDV,SAAS,CAACa,EAAE,EAAE,CAAC;MACrF;IACF;IAEA;IACA,IAAIb,SAAS,CAACnF,SAAS,EAAE;MACvBiU,YAAY,CAACE,SAAS,CAAChL,GAAG,CAAC,WAAW,CAAC;IACzC,CAAC,MAAM;MACL8K,YAAY,CAACE,SAAS,CAACC,MAAM,CAAC,WAAW,CAAC;IAC5C;IAEA;IACA,MAAMC,cAAc,GAAGJ,YAAY,CAAC5I,gBAAgB,CAAC,eAAe,CAAC;IACrE,IAAIgJ,cAAc,IAAIA,cAAc,CAACnP,MAAM,GAAG,CAAC,EAAE;MAC/C;MACA,MAAMb,KAAK,GAAG,IAAIC,IAAI,EAAE;MACxBD,KAAK,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC1B,MAAMjE,YAAY,GAAG,IAAIgE,IAAI,CAAC,IAAI,CAAChE,YAAY,CAAC;MAChDA,YAAY,CAACiE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACjC,MAAMC,eAAe,GAAGlE,YAAY,CAACmE,OAAO,EAAE,KAAKJ,KAAK,CAACI,OAAO,EAAE;MAElE;MACA,IAAID,eAAe,EAAE;QACnB,MAAM8P,WAAW,GAAGnP,SAAS,CAAC1F,MAAM,IAAI,CAAC;QACzC0I,OAAO,CAACC,GAAG,CAAC,yBAAyBjD,SAAS,CAACa,EAAE,gBAAgBb,SAAS,CAACnF,SAAS,aAAasU,WAAW,EAAE,CAAC;QAE/G;QACAD,cAAc,CAACzK,OAAO,CAAC2K,OAAO,IAAG;UAC/B,IAAIA,OAAO,CAACC,aAAa,IAAID,OAAO,CAACC,aAAa,CAACC,QAAQ,CAACF,OAAO,CAAC,EAAE;YACpE;YACCA,OAAuB,CAACvI,KAAK,CAAC0I,OAAO,GAAG,OAAO;YAChDH,OAAO,CAACI,WAAW,GAAG,KAAKL,WAAW,GAAG;UAC3C;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACAD,cAAc,CAACzK,OAAO,CAAC2K,OAAO,IAAG;UAC/B,IAAIA,OAAO,CAACC,aAAa,IAAID,OAAO,CAACC,aAAa,CAACC,QAAQ,CAACF,OAAO,CAAC,EAAE;YACnEA,OAAuB,CAACvI,KAAK,CAAC0I,OAAO,GAAG,MAAM;YAC/CH,OAAO,CAACI,WAAW,GAAG,EAAE;UAC1B;QACF,CAAC,CAAC;MACJ;IACF;IAEA;IACA,MAAMC,YAAY,GAAGX,YAAY,CAACC,aAAa,CAAC,gBAAgB,CAAC;IACjE,IAAIU,YAAY,EAAE;MAChB,MAAMW,QAAQ,GAAGpQ,SAAS,CAACtE,aAAa,CAAC5B,SAAS,KAAK,OAAO,GAAG,IAAIkG,SAAS,CAACtE,aAAa,CAAC5B,SAAS,EAAE,GAAG,EAAE;MAC7G2V,YAAY,CAACD,WAAW,GAAG,GAAGxP,SAAS,CAAC5G,cAAc,IAAI4G,SAAS,CAACtE,aAAa,CAAC/B,UAAU,GAAGyW,QAAQ,EAAE;IAC3G;IAEA;IACAlM,UAAU,CAAC,MAAK;MACd,IAAI4K,YAAY,CAACO,aAAa,EAAE;QAC9B,MAAME,OAAO,GAAGT,YAAY,CAACO,aAAa,CAACxI,KAAK,CAAC0I,OAAO;QACxDT,YAAY,CAACO,aAAa,CAACxI,KAAK,CAAC0I,OAAO,GAAG,MAAM;QACjD;QACA,KAAKT,YAAY,CAACO,aAAa,CAACgB,YAAY;QAC5CvB,YAAY,CAACO,aAAa,CAACxI,KAAK,CAAC0I,OAAO,GAAGA,OAAO;MACpD;IACF,CAAC,EAAE,CAAC,CAAC;IAELvM,OAAO,CAACC,GAAG,CAAC,wCAAwCjD,SAAS,CAACa,EAAE,EAAE,CAAC;EACrE;EAEAyP,iBAAiBA,CAACtC,KAAY;IAC5BA,KAAK,CAACuC,cAAc,EAAE;IACtB,IAAI,CAAChP,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAAC1F,QAAQ,GAAG,IAAI,CAAC2F,aAAa,EAAE;IACpC,IAAI,CAACM,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAE7B;IACA,IAAI,CAAC/F,oBAAoB,GAAG,KAAK;EACnC;EAEAkB,kBAAkBA,CAAA;IAChB,IAAI,CAACqE,iBAAiB,GAAG,KAAK;IAC9B;IACA,IAAI,CAAC1F,QAAQ,GAAG,IAAI,CAAC2F,aAAa,EAAE;IACpC,IAAI,CAACM,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAAC/F,oBAAoB,GAAG,KAAK;EACnC;EAEMoB,WAAWA,CAAA;IAAA,IAAAoT,MAAA;IAAA,OAAAxL,iBAAA;MACf,IAAI,CAACwL,MAAI,CAAChR,MAAM,IAAI,CAACgR,MAAI,CAAC3U,QAAQ,CAACb,IAAI,IAAI,CAACwV,MAAI,CAAC3U,QAAQ,CAACf,KAAK,IAAI,CAAC0V,MAAI,CAAC3U,QAAQ,CAAC4B,UAAU,IAC1F,CAAC+S,MAAI,CAAC3U,QAAQ,CAAC8B,QAAQ,IAAI,CAAC6S,MAAI,CAAC3U,QAAQ,CAAClC,UAAU,IAAI,CAAC6W,MAAI,CAAC3U,QAAQ,CAAC/B,SAAS,IAAI,CAAC0W,MAAI,CAAC3U,QAAQ,CAACqC,WAAW,EAAE;QAChH8E,OAAO,CAACtC,KAAK,CAAC,0DAA0D,CAAC;QACzE;MACF;MAEA,IAAI;QACF;QACA,IAAI8P,MAAI,CAAC3U,QAAQ,CAACqC,WAAW,KAAK,MAAM,IAAIsS,MAAI,CAAC1O,kBAAkB,CAAC/B,MAAM,GAAG,CAAC,EAAE;UAC9EyQ,MAAI,CAAC3U,QAAQ,CAACiQ,iBAAiB,GAAG0E,MAAI,CAAC1O,kBAAkB,CAAC2O,IAAI,CAAC,GAAG,CAAC;QACrE,CAAC,MAAM,IAAID,MAAI,CAAC3U,QAAQ,CAACqC,WAAW,KAAK,OAAO,IAAIsS,MAAI,CAACzO,mBAAmB,CAAChC,MAAM,GAAG,CAAC,EAAE;UACvFyQ,MAAI,CAAC3U,QAAQ,CAACkQ,kBAAkB,GAAGyE,MAAI,CAACzO,mBAAmB,CAAC0O,IAAI,CAAC,GAAG,CAAC;QACvE;QAEA;QACAzN,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAEuN,MAAI,CAAChR,MAAM,CAAC;QAC9E,MAAM;UAAEoB,IAAI,EAAE8P,WAAW;UAAEhQ,KAAK,EAAEiQ;QAAS,CAAE,SAASH,MAAI,CAACvQ,eAAe,CAACC,SAAS,EAAE,CACnFC,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC,IAAI,CAAC,CACZC,EAAE,CAAC,IAAI,EAAEmQ,MAAI,CAAChR,MAAM,CAAC,CACrBe,MAAM,EAAE;QAEX,IAAIoQ,SAAS,IAAI,CAACD,WAAW,EAAE;UAC7B1N,OAAO,CAACtC,KAAK,CAAC,oCAAoC,EAAEiQ,SAAS,IAAI,kBAAkB,CAAC;UACpF,MAAM,IAAIC,KAAK,CAAC,0DAA0D,CAAC;QAC7E;QAEA5N,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEyN,WAAW,CAAC;QAE1D;QACA;QACA,MAAMG,aAAa,GAAgD;UACjE7V,IAAI,EAAEwV,MAAI,CAAC3U,QAAQ,CAACb,IAAI,IAAI,EAAE;UAC9BC,WAAW,EAAEuV,MAAI,CAAC3U,QAAQ,CAACZ,WAAW,IAAI,EAAE;UAC5CwC,UAAU,EAAE+S,MAAI,CAAC3U,QAAQ,CAAC4B,UAAU,IAAI,OAAO;UAC/C9D,UAAU,EAAE6W,MAAI,CAAC3U,QAAQ,CAAClC,UAAU,IAAI,CAAC;UACzCG,SAAS,EAAE0W,MAAI,CAAC3U,QAAQ,CAAC/B,SAAS,IAAI,OAAO;UAC7CoE,WAAW,EAAEsS,MAAI,CAAC3U,QAAQ,CAACqC,WAAW,IAAI,KAAK;UAC/CpC,QAAQ,EAAE0U,MAAI,CAAC3U,QAAQ,CAACC,QAAQ,IAAI,OAAO;UAC3C6B,QAAQ,EAAE6S,MAAI,CAAC3U,QAAQ,CAAC8B,QAAQ,IAAI,UAAU;UAC9C7C,KAAK,EAAE0V,MAAI,CAAC3U,QAAQ,CAACf,KAAK,IAAI,IAAI;UAClCgR,iBAAiB,EAAE0E,MAAI,CAAC3U,QAAQ,CAACiQ,iBAAiB,IAAI,EAAE;UACxDC,kBAAkB,EAAEyE,MAAI,CAAC3U,QAAQ,CAACkQ,kBAAkB,IAAI,EAAE;UAC1D+E,OAAO,EAAEN,MAAI,CAAChR,MAAM;UAAE;UACtBwM,MAAM,EAAE;SACT;QAEDhJ,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE4N,aAAa,CAAC;QAExD,IAAI;UACF,MAAME,OAAO,SAASP,MAAI,CAACxO,YAAY,CAAC5E,WAAW,CAACyT,aAAa,CAAC;UAClE7N,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAE8N,OAAO,CAAC;UAEtE;UACA,IAAIP,MAAI,CAAC3U,QAAQ,CAAC4B,UAAU,KAAK,MAAM,EAAE;YACvCuF,OAAO,CAACC,GAAG,CAAC,yEAAyE,CAAC;YAEtF;YACA,MAAMuN,MAAI,CAACxO,YAAY,CAACqM,qBAAqB,CAC3CmC,MAAI,CAAChR,MAAM,EACXuR,OAAO,EACP,IAAI5R,IAAI,EAAE;YAAE;YACZ,CAAC;YAAE;YACH;cAAE,GAAG0R,aAAa;cAAEhQ,EAAE,EAAEkQ;YAAO,CAAW,CAC3C;UACH;UAEA;UACA,MAAMvL,OAAO,GAAGgL,MAAI,CAACzR,UAAU,CAACyR,MAAI,CAACrV,YAAY,CAAC;UAClD,OAAOqV,MAAI,CAACnP,UAAU,CAACmE,OAAO,CAAC;UAC/B,OAAOgL,MAAI,CAACjO,iBAAiB,CAACiD,OAAO,CAAC;UACtCxC,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEuC,OAAO,CAAC;UAE1DgL,MAAI,CAACtT,kBAAkB,EAAE;UACzBsT,MAAI,CAACjN,QAAQ,EAAE;QACjB,CAAC,CAAC,OAAOyN,UAAe,EAAE;UACxBhO,OAAO,CAACtC,KAAK,CAAC,kCAAkC,EAAEsQ,UAAU,CAAC;UAE7D;UACA,IAAIA,UAAU,CAACC,OAAO,IAAID,UAAU,CAACC,OAAO,CAACtE,QAAQ,CAAC,wBAAwB,CAAC,EAAE;YAC/EuE,KAAK,CAAC,uJAAuJ,CAAC;UAChK,CAAC,MAAM,IAAIF,UAAU,CAACC,OAAO,IAAID,UAAU,CAACC,OAAO,CAACtE,QAAQ,CAAC,2BAA2B,CAAC,EAAE;YACzFuE,KAAK,CAACF,UAAU,CAACC,OAAO,CAAC;UAC3B,CAAC,MAAM;YACLC,KAAK,CAAC,yBAAyBF,UAAU,CAACC,OAAO,EAAE,CAAC;UACtD;QACF;MACF,CAAC,CAAC,OAAOvQ,KAAU,EAAE;QACnBsC,OAAO,CAACtC,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxDwQ,KAAK,CAAC,UAAUxQ,KAAK,CAACuQ,OAAO,IAAI,wBAAwB,EAAE,CAAC;MAC9D;IAAC;EACH;EAEA7U,gBAAgBA,CAAC4R,KAAU,EAAE9C,GAAW;IACtC;IACA,IAAIiG,SAAS,GAAG,KAAK;IAErB,IAAInD,KAAK,CAACG,MAAM,KAAKjL,SAAS,EAAE;MAC9B;MACAiO,SAAS,GAAGnD,KAAK,CAACG,MAAM,CAACiD,OAAO;IAClC,CAAC,MAAM,IAAIpD,KAAK,CAACxU,MAAM,YAAY4M,gBAAgB,EAAE;MACnD;MACA+K,SAAS,GAAGnD,KAAK,CAACxU,MAAM,CAAC4X,OAAO;IAClC;IAEA,IAAID,SAAS,EAAE;MACb,IAAI,CAACrP,kBAAkB,CAAC0C,IAAI,CAAC0G,GAAG,CAAC;IACnC,CAAC,MAAM;MACL,MAAMI,KAAK,GAAG,IAAI,CAACxJ,kBAAkB,CAACuP,OAAO,CAACnG,GAAG,CAAC;MAClD,IAAII,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,IAAI,CAACxJ,kBAAkB,CAACwP,MAAM,CAAChG,KAAK,EAAE,CAAC,CAAC;MAC1C;IACF;IAEAtI,OAAO,CAACC,GAAG,CAAC,oCAAoC,IAAI,CAACnB,kBAAkB,CAAC2O,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;EACvF;EAEA5T,iBAAiBA,CAACmR,KAAU,EAAE9C,GAAW;IACvC;IACA,IAAIiG,SAAS,GAAG,KAAK;IAErB,IAAInD,KAAK,CAACG,MAAM,KAAKjL,SAAS,EAAE;MAC9B;MACAiO,SAAS,GAAGnD,KAAK,CAACG,MAAM,CAACiD,OAAO;IAClC,CAAC,MAAM,IAAIpD,KAAK,CAACxU,MAAM,YAAY4M,gBAAgB,EAAE;MACnD;MACA+K,SAAS,GAAGnD,KAAK,CAACxU,MAAM,CAAC4X,OAAO;IAClC;IAEA,IAAID,SAAS,EAAE;MACb,IAAI,CAACpP,mBAAmB,CAACyC,IAAI,CAAC0G,GAAG,CAAC;IACpC,CAAC,MAAM;MACL,MAAMI,KAAK,GAAG,IAAI,CAACvJ,mBAAmB,CAACsP,OAAO,CAACnG,GAAG,CAAC;MACnD,IAAII,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,IAAI,CAACvJ,mBAAmB,CAACuP,MAAM,CAAChG,KAAK,EAAE,CAAC,CAAC;MAC3C;IACF;IAEAtI,OAAO,CAACC,GAAG,CAAC,qCAAqC,IAAI,CAAClB,mBAAmB,CAAC0O,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;EACzF;EAEArS,mBAAmBA,CAAA;IACjB;IACA,IAAI,CAAC0D,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7BiB,OAAO,CAACC,GAAG,CAAC,gCAAgC,IAAI,CAACpH,QAAQ,CAACqC,WAAW,oBAAoB,CAAC;EAC5F;EAEAL,qBAAqBA,CAACmQ,KAAW;IAC/B,IAAI,CAAC,IAAI,CAACxO,MAAM,IAAI,CAAC,IAAI,CAAC3D,QAAQ,CAAC8B,QAAQ,EAAE;IAE7C;IACA,IAAIqQ,KAAK,IAAIA,KAAK,CAACG,MAAM,EAAE;MACzB,IAAI,CAACtS,QAAQ,CAAC8B,QAAQ,GAAGqQ,KAAK,CAACG,MAAM,CAAC9R,KAAK;MAC3C2G,OAAO,CAACC,GAAG,CAAC,kCAAkC,IAAI,CAACpH,QAAQ,CAAC8B,QAAQ,kBAAkB,CAAC;IACzF;IAEA;IACA,IAAI,CAACqE,YAAY,CAAC8F,SAAS,CAAC,IAAI,CAACtI,MAAM,CAAC,CAACG,IAAI,CAC3CnJ,IAAI,CAAC,CAAC,CAAC,EACPH,GAAG,CAAC+K,MAAM,IAAG;MACX,OAAOA,MAAM,CAACmQ,IAAI,CAAC9F,CAAC,IAClBA,CAAC,CAAC9N,QAAQ,KAAK,IAAI,CAAC9B,QAAQ,CAAC8B,QAAQ,IACrC8N,CAAC,CAAC3P,QAAQ,KAAK,MAAM,IACrB2P,CAAC,CAACO,MAAM,CACT;IACH,CAAC,CAAC,CACH,CAACpM,SAAS,CAAC;MACVC,IAAI,EAAG2R,eAAe,IAAI;QACxB,IAAI,CAACxV,oBAAoB,GAAGwV,eAAe;QAE3C;QACA,IAAIA,eAAe,EAAE;UACnB,IAAI,CAAC3V,QAAQ,CAACC,QAAQ,GAAG,OAAO;QAClC;QAEAkH,OAAO,CAACC,GAAG,CAAC,uBAAuB,IAAI,CAACpH,QAAQ,CAAC8B,QAAQ,6BAA6B6T,eAAe,EAAE,CAAC;MAC1G;KACD,CAAC;EACJ;EAEA;;;EAGAzH,uBAAuBA,CAAC3I,MAAsB;IAC5C;IACA,MAAMlC,KAAK,GAAG,IAAIC,IAAI,EAAE;IACxBD,KAAK,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC1B,MAAMjE,YAAY,GAAG,IAAIgE,IAAI,CAAC,IAAI,CAAChE,YAAY,CAAC;IAChDA,YAAY,CAACiE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACjC,MAAMC,eAAe,GAAGlE,YAAY,CAACmE,OAAO,EAAE,KAAKJ,KAAK,CAACI,OAAO,EAAE;IAClE,MAAM8E,QAAQ,GAAG,IAAI,CAACrF,UAAU,CAACG,KAAK,CAAC;IAEvC,IAAI,CAACG,eAAe,IAAI,CAAC,IAAI,CAACP,WAAW,EAAE;MACzC;IACF;IAEA;IACA,MAAM2S,gBAAgB,GAAGnN,YAAY,CAACO,OAAO,CAAC,qBAAqBT,QAAQ,EAAE,CAAC;IAC9E,IAAIqN,gBAAgB,EAAE;MACpBzO,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEmB,QAAQ,CAAC;MACxE;IACF;IAEA;IACA,MAAMsN,kBAAkB,GAAGtQ,MAAM,CAACrB,MAAM,GAAG,CAAC,IAAIqB,MAAM,CAACuQ,KAAK,CAAC3J,KAAK,IAAIA,KAAK,CAACnN,SAAS,CAAC;IAEtF;IACA,MAAM+W,kBAAkB,GAAG,CAAC,IAAI,CAACrS,cAAc,IAAI,CAAC,IAAI,CAAClE,UAAU,IAAI,IAAI,CAACA,UAAU,CAACR,SAAS;IAEhG;IAEA,IAAI6W,kBAAkB,IAAIE,kBAAkB,IAAI,IAAI,CAAC9S,WAAW,CAAC+S,gBAAgB,EAAE;MACjF;MACA,IAAI,CAAC5P,WAAW,CAAC0B,WAAW,CAAC,IAAI,CAACnE,MAAO,CAAC,CAACI,SAAS,CAACgE,QAAQ,IAAG;QAC9D,IAAIA,QAAQ,EAAE;UACZ,IAAI,CAAC9E,WAAW,GAAG8E,QAAQ;QAC7B;QAEA;QACA,IAAI,CAACnC,eAAe,GAAG,IAAI;QAE3B;QACA6C,YAAY,CAACwN,OAAO,CAAC,qBAAqB1N,QAAQ,EAAE,EAAE,MAAM,CAAC;QAE7D;QACA,IAAI,CAAC,IAAI,CAAC1C,qBAAqB,CAACiL,QAAQ,CAACvI,QAAQ,CAAC,EAAE;UAClD,IAAI,CAAC1C,qBAAqB,CAAC8C,IAAI,CAACJ,QAAQ,CAAC;QAC3C;MACF,CAAC,CAAC;IACJ;EACF;EAEA;;;EAGAvF,gBAAgBA,CAAA;IACd,IAAI,CAAC4C,eAAe,GAAG,KAAK;EAC9B;EAIA;EACA1C,UAAUA,CAAC8G,IAAU;IACnB,MAAMkM,IAAI,GAAGlM,IAAI,CAACmM,WAAW,EAAE;IAC/B,MAAMlE,KAAK,GAAGmE,MAAM,CAACpM,IAAI,CAACqM,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAMjH,GAAG,GAAG+G,MAAM,CAACpM,IAAI,CAACyE,OAAO,EAAE,CAAC,CAAC6H,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACnD,OAAO,GAAGJ,IAAI,IAAIjE,KAAK,IAAI5C,GAAG,EAAE;EAClC;EAEAhQ,SAASA,CAACkX,KAAW,EAAEC,KAAW;IAChC,OAAOD,KAAK,CAACJ,WAAW,EAAE,KAAKK,KAAK,CAACL,WAAW,EAAE,IAChDI,KAAK,CAACF,QAAQ,EAAE,KAAKG,KAAK,CAACH,QAAQ,EAAE,IACrCE,KAAK,CAAC9H,OAAO,EAAE,KAAK+H,KAAK,CAAC/H,OAAO,EAAE;EACvC;EAEAlP,QAAQA,CAAA;IACN,MAAM8D,KAAK,GAAG,IAAIC,IAAI,EAAE;IACxBD,KAAK,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC1B,OAAOF,KAAK;EACd;EAEA;EACQqN,eAAeA,CAAC+F,cAAsB;IAC5C;IACA,MAAMC,MAAM,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzD,OAAOA,MAAM,CAACD,cAAc,CAAC;EAC/B;EAEA;EACQ7F,cAAcA,CAAC6F,cAAsB;IAC3C;IACA,MAAMC,MAAM,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAChE,OAAOA,MAAM,CAACD,cAAc,CAAC;EAC/B;EAEQ9Q,aAAaA,CAAA;IACnB,OAAO;MACLxG,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfwC,UAAU,EAAE,OAAoB;MAChC9D,UAAU,EAAE,CAAC;MACbG,SAAS,EAAE,OAAwB;MACnCoE,WAAW,EAAE,KAAoB;MACjCpC,QAAQ,EAAE,OAAwB;MAAE;MACpC6B,QAAQ,EAAE,EAAmB;MAC7B7C,KAAK,EAAE;KACR;EACH;;aAp4DWkE,SAAS;;mCAATA,UAAS;AAAA;;QAATA,UAAS;EAAAwT,SAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,mBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCzDpB5b,EADF,CAAAK,cAAA,qBAAqD,aACjB;MAEhCL,EADA,CAAAC,SAAA,aAA+B,aACG;MACpCD,EAAA,CAAAO,YAAA,EAAM;MAEJP,EADF,CAAAK,cAAA,oBAAkC,kBACnB;MACXL,EAAA,CAAAC,SAAA,oBAAmD;MACnDD,EAAA,CAAAK,cAAA,iBAA0C;MACxCL,EAAA,CAAAkB,UAAA,IAAA4a,wBAAA,kBAAyE;MAoB/E9b,EAFI,CAAAO,YAAA,EAAU,EACE,EACH;MAEXP,EADF,CAAAK,cAAA,eAAU,kBACoC;MAC1CL,EAAA,CAAAC,SAAA,eAAkD;MACpDD,EAAA,CAAAO,YAAA,EAAU;MAGNP,EAFJ,CAAAK,cAAA,mBAA2B,eAChB,UACH;MAAAL,EAAA,CAAAM,MAAA,cAAM;MACZN,EADY,CAAAO,YAAA,EAAK,EACP;MAERP,EADF,CAAAK,cAAA,eAAS,sBAC+F;MAApCL,EAAA,CAAAQ,UAAA,mBAAAub,gDAAAja,MAAA;QAAA,OAAS+Z,GAAA,CAAAzC,iBAAA,CAAAtX,MAAA,CAAyB;MAAA,EAAC;MACnG9B,EAAA,CAAAM,MAAA,qBACF;MAEJN,EAFI,CAAAO,YAAA,EAAa,EACL,EACF;MAERP,EADF,CAAAK,cAAA,mBAAwB,eACb;MAgBPL,EAfA,CAAAkB,UAAA,KAAA8a,8BAAA,wBAA4E,KAAAC,8BAAA,yBAgBoB;MAmDpGjc,EADE,CAAAO,YAAA,EAAU,EACF;MAGNP,EAFJ,CAAAK,cAAA,mBAAiC,eACtB,UACH;MAAAL,EAAA,CAAAM,MAAA,wBAAgB;MAExBN,EAFwB,CAAAO,YAAA,EAAK,EACjB,EACF;MAERP,EADF,CAAAK,cAAA,mBAAwB,eACb;MA2BPL,EA1BA,CAAAkB,UAAA,KAAAgb,8BAAA,wBAEwC,KAAAC,8BAAA,uBAwBS;MAcvDnc,EAFI,CAAAO,YAAA,EAAU,EACF,EACD;MAGXP,EAAA,CAAAK,cAAA,qBAC8F;MAD3CL,EAAA,CAAAQ,UAAA,gCAAA4b,4DAAA;QAAA,OAAsBP,GAAA,CAAA7V,kBAAA,EAAoB;MAAA,EAAC;MAE5FhG,EAAA,CAAAkB,UAAA,KAAAmb,iCAAA,yBAAa;MAyQfrc,EAAA,CAAAO,YAAA,EAAY;MAEZP,EAAA,CAAAkB,UAAA,KAAAob,qCAAA,8BAC+B;MAEjCtc,EAAA,CAAAO,YAAA,EAAc;MACdP,EAAA,CAAAC,SAAA,sBAAiC;;;MArbAD,EAAA,CAAA2B,UAAA,oBAAmB;MAOlC3B,EAAA,CAAAoB,SAAA,GAAyB;MAAzBpB,EAAA,CAAA2B,UAAA,eAAAka,GAAA,CAAA7R,UAAA,CAAyB;MAEShK,EAAA,CAAAoB,SAAA,GAAc;MAAdpB,EAAA,CAAA2B,UAAA,YAAAka,GAAA,CAAA/R,SAAA,CAAc;MAqC/C9J,EAAA,CAAAoB,SAAA,IAAyB;MAAzBpB,EAAA,CAAA2B,UAAA,SAAAka,GAAA,CAAA3R,MAAA,CAAArB,MAAA,OAAyB;MAeR7I,EAAA,CAAAoB,SAAA,EAAS;MAATpB,EAAA,CAAA2B,UAAA,YAAAka,GAAA,CAAA3R,MAAA,CAAS;MA4D1BlK,EAAA,CAAAoB,SAAA,GAA4C;MAA5CpB,EAAA,CAAA2B,UAAA,SAAAka,GAAA,CAAA1X,UAAA,IAAA0X,GAAA,CAAA1X,UAAA,CAAAK,aAAA,CAA4C;MA0BzBxE,EAAA,CAAAoB,SAAA,EAAiB;MAAjBpB,EAAA,CAAA2B,UAAA,UAAAka,GAAA,CAAA1X,UAAA,CAAiB;MAiB1CnE,EAAA,CAAAoB,SAAA,EAAe;MACyCpB,EADxD,CAAA2B,UAAA,gBAAe,yBACA,gBAAA3B,EAAA,CAAAwB,eAAA,KAAA+a,GAAA,EAAwC,2BAA2B;MA4Q3Evc,EAAA,CAAAoB,SAAA,GAAqB;MAArBpB,EAAA,CAAA2B,UAAA,SAAAka,GAAA,CAAAtR,eAAA,CAAqB;;;iBDzX7B5L,WAAW,EAAA6d,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,UAAA,EAAAF,EAAA,CAAAG,OAAA,EAAAH,EAAA,CAAAI,cAAA,EAAAJ,EAAA,CAAAK,aAAA,EAAAL,EAAA,CAAAM,YAAA,EAAAN,EAAA,CAAAO,WAAA,EAAAP,EAAA,CAAAQ,MAAA,EAAAR,EAAA,CAAAS,UAAA,EAAAT,EAAA,CAAAU,SAAA,EAAAV,EAAA,CAAAW,OAAA,EAAAX,EAAA,CAAAY,SAAA,EAAAZ,EAAA,CAAAa,OAAA,EAAAb,EAAA,CAAAc,QAAA,EAAAd,EAAA,CAAAe,QAAA,EAAAf,EAAA,CAAAgB,MAAA,EAAAhB,EAAA,CAAAiB,SAAA,EAAAjB,EAAA,CAAAkB,eAAA,EAAAlB,EAAA,CAAAmB,OAAA,EAAAnB,EAAA,CAAAoB,WAAA,EAAApB,EAAA,CAAAqB,QAAA,EAAArB,EAAA,CAAAsB,UAAA,EAAAtB,EAAA,CAAAuB,QAAA,EAAAvB,EAAA,CAAAwB,oBAAA,EAAAxB,EAAA,CAAAyB,oBAAA,EAAAzB,EAAA,CAAA0B,mBAAA,EAAA1B,EAAA,CAAA2B,iBAAA,EAAA3B,EAAA,CAAA4B,eAAA,EAAE3f,YAAY,EAAA4f,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE7f,WAAW,EAAA8f,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,oBAAA,EAAAH,EAAA,CAAAI,iBAAA,EAAAJ,EAAA,CAAAK,OAAA,EAAAL,EAAA,CAAAM,MAAA,EAAEtf,mBAAmB,EAAEC,oBAAoB,EAAEI,mBAAmB,EAAEE,eAAe;EAAAgf,MAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}