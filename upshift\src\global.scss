/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

/* Core CSS required for Ionic components to work properly */
@import "@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "@ionic/angular/css/normalize.css";
@import "@ionic/angular/css/structure.css";
@import "@ionic/angular/css/typography.css";
@import "@ionic/angular/css/display.css";

/* Optional CSS utils that can be commented out */
@import "@ionic/angular/css/padding.css";
@import "@ionic/angular/css/float-elements.css";
@import "@ionic/angular/css/text-alignment.css";
@import "@ionic/angular/css/text-transformation.css";
@import "@ionic/angular/css/flex-utils.css";

/**
 * Ionic Dark Mode
 * -----------------------------------------------------
 * For more info, please see:
 * https://ionicframework.com/docs/theming/dark-mode
 */

/* @import "@ionic/angular/css/palettes/dark.always.css"; */
/* @import "@ionic/angular/css/palettes/dark.class.css"; */
@import "@ionic/angular/css/palettes/dark.system.css";

* {
  font-family: var(--ion-font-family);
}

:root {
  --bg: #0a0b0f;              /* Main background - darker, more neutral */
  --surface: #16171c;         /* Cards and containers */
  --surface-alt: #1e1f25;     /* Slight variation for subtle contrast */
  --accent: #4169e1;          /* Royal blue accent color */
  --accent-hover: #5277e8;    /* Lighter accent for hover/focus */
  --accent-glow: #4169e133;   /* Soft glow for progress bars */
  --text: #ffffff;            /* High contrast text */
  --text-secondary: #a0a3b1;  /* Desaturated for subtitles, metadata */
  --text-muted: #6b6d7c;      /* Hints, timestamps, soft elements */
  --border: #26272e;          /* Card and element borders */
  --success: #32d690;         /* Completed or positive status */
  --warning: #facc15;         /* Time-sensitive tasks, alerts */
  --error: #ef4444;           /* Incomplete/failed task indicators */
  --quest-card-bg: rgba(30, 31, 37, 0.5);  /* Semi-transparent quest cards */
  --progress-bg: rgba(65, 105, 225, 0.1);  /* Progress bar background */

  /* App Rating Alert Variables */
  --rating-alert-bg: var(--surface);
  --rating-alert-border: var(--border);
  --rating-alert-text: var(--text);
  --rating-alert-button-bg: var(--accent);
  --rating-alert-button-text: var(--text);
}

/* App Rating Alert Styles */
.app-rating-alert {
  --background: var(--rating-alert-bg);
  --border-color: var(--rating-alert-border);
  --border-radius: 16px;
  --box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);

  .alert-head {
    padding-top: 24px;

    .alert-title {
      font-size: 20px;
      font-weight: 700;
      color: var(--rating-alert-text);
    }
  }

  .alert-message {
    color: var(--text-secondary);
    font-size: 16px;
    padding: 0 16px 16px;
  }

  .alert-button-group {
    padding: 16px;

    .alert-button {
      border-radius: 0;

      &.alert-button-confirm {
        --background: var(--rating-alert-button-bg);
        --color: var(--rating-alert-button-text);
        font-weight: 600;
      }

      &.alert-button-cancel {
        --color: var(--text-secondary);
      }
    }
  }
}

ion-toolbar {
  --background: transparent;
}

body {
  background-color: var(--bg) !important;
  color: var(--text-color);
  min-height: 100vh;
  overflow-y: auto;
  padding-bottom: 70px; /* Space for bottom navigation */
}

/* Global styles for cards and containers */
ion-card, .card-container {
  background: var(--quest-card-bg);
  border: 1px solid var(--border);
  border-radius: 12px;
}

/* Progress bar styling */
.progress-bar {
  background: var(--progress-bg);
  border-radius: 6px;
  overflow: hidden;
}

.progress-bar .fill {
  background: linear-gradient(90deg, var(--accent), var(--accent-hover));
  box-shadow: 0 0 15px var(--accent-glow);
}

/* Quest icons and badges */
.quest-icon {
  color: var(--accent);
  filter: drop-shadow(0 0 8px var(--accent-glow));
}

/* Global text styles */
h1, h2, h3, h4, h5, h6 {
  color: var(--text);
  font-weight: 600;
  margin: 0;
}

p, span {
  color: var(--text-secondary);
}

.muted {
  color: var(--text-muted);
}

//global settings//

//buttons

ion-button {
  width: 100%;
  height: 50px;
}

.blue-button {
  --background: var(--accent);
  --color: var(--text);
  margin: 0;
  font-weight: 600;
  font-size: 15px;
}

.social-button {
  width: 100%;
  flex: 1;
  --background: var(--surface);
  --color: var(--text);
  --border-color: var(--border);
  --border-style: solid;
  --border-width: 1px;

  .button-content {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;

    ion-icon {
      font-size: 18px;
      margin-right: 8px;
    }

    span {
      font-size: 15px;
      font-weight: 500;
    }
  }
}

//inputs//

.dark-input {
  --background: var(--surface);
  --placeholder-color: var(--text);
  --color: var(--text);
  --border-color: var(--border);
  --border-style: solid;
  --border-radius: 8px;
  --padding-start: 16px;
  --padding-end: 16px;
  --padding-top: 14px;
  --padding-bottom: 14px;
  --border-width: 1px;
  --font-size: 16px;
  --height: 40px;
}

ion-input.custom.ios .input-bottom .helper-text,
ion-input.custom.ios .input-bottom .counter,
ion-input.custom.md .input-bottom .helper-text,
ion-input.custom.md .input-bottom .counter {
  color: var(--text);
}

//texts//

.upshift-title {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 24px;
  color: var(--text);
}

.gradient-text {
  background: linear-gradient(
    270deg,
    var(--accent),
    var(--text),
    var(--accent)
  );
  background-size: 300% 100%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradient 3s ease-in-out infinite alternate;
}

@keyframes gradient {
  0% { background-position: 100% 0%; }
  100% { background-position: 0% 0%; }
}

.dark-text {
  color: var(--text-secondary);
  font-size: 18px;
  line-height: 1.6;
}

//sun background//
.background-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: -1;

  .gradient-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg);
  }

  .celestial-body {
    position: absolute;
    width: 250px;
    height: 250px;
    border-radius: 50%;
    filter: blur(25px);
    background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0) 70%);
    animation: celestial-cycle 30s ease-in-out infinite;
    opacity: 0.5;
    box-shadow: 0 0 60px rgba(255, 255, 255, 0.1);
  }

  @keyframes celestial-cycle {
    0% {
      transform: translate(-150px, 120%);
      background: radial-gradient(circle, rgba(255,190,69,0.4) 0%, rgba(255,190,69,0) 70%);
      box-shadow: 0 0 80px rgba(255, 165, 0, 0.2);
    }
    25% {
      transform: translate(0, 0);
      background: radial-gradient(circle, rgba(233,212,162,0.4) 0%, rgba(233,212,162,0) 70%);
      box-shadow: 0 0 80px rgba(214, 190, 115, 0.2);
    }
    50% {
      transform: translate(120%, -150px);
      background: radial-gradient(circle, rgba(210,235,255,0.4) 0%, rgba(210,235,255,0) 70%);
      box-shadow: 0 0 80px rgba(173, 216, 230, 0.2);
    }
    75% {
      transform: translate(0, 0);
      background: radial-gradient(circle, rgba(233,212,162,0.4) 0%, rgba(233,212,162,0) 70%);
      box-shadow: 0 0 80px rgba(214, 190, 115, 0.2);
    }
    100% {
      transform: translate(-150px, 120%);
      background: radial-gradient(circle, rgba(255,190,69,0.4) 0%, rgba(255,190,69,0) 70%);
      box-shadow: 0 0 80px rgba(255, 165, 0, 0.2);
    }
  }

}

//accessories//

.divider {
  display: flex;
  align-items: center;
  margin: 20px 0;
  color: var(--text-secondary);
  font-size: 14px;

  &:before, &:after {
    content: "";
    flex: 1;
    height: 1px;
    background: var(--border);
    opacity: 0.7;
  }

  span {
    padding: 0 16px;
  }
}

ion-card {
  border-radius: 8px;

}
