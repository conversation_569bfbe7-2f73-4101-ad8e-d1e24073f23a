ion-content {
  .date {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin: 0 auto;
    position: relative;

    .date-progress {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      pointer-events: none;
      z-index: 0;
      transform: rotate(-90deg);
    }

    .date-progress circle {
      fill: transparent;
      stroke-width: 5;
      stroke-linecap: round;
      transform-origin: center;
      transition: stroke-dasharray 0.5s ease;
    }

    .date-progress .background-circle {
      stroke: rgba(255, 255, 255, 0.1);
      stroke-width: 5;
    }

    .date-progress .progress-circle {
      stroke: #4169E1;
      stroke-opacity: 1;
      stroke-width: 5;
    }

    .date-progress .progress-circle.low {
      stroke: #FF9500 !important;
    }
  }

  ion-header {
    .week-row {
      display: flex;
      justify-content: space-around;

      .day-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 40px;

        .day-name {
          padding: 5px;
          font-size: 12px;
          color: white;
          margin-bottom: 8px;
          font-weight: lighter;
          width: 22px;
          height: 22px;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .day-name.active {
          background-color: var(--accent);
        }

        .day-name.selected {
          background-color: var(--text-muted);
        }

        .date-progress .progress-circle {
          stroke: #4169E1;
          stroke-opacity: 0.9;
        }

        .date.disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        .date.unselected {
          color: var(--text-muted);
        }
      }
    }
  }

  ion-grid {
    padding: 0;

    .heartbeat-circle {
      margin: 32px;
      width: 100px;
      height: 100px;
      background: linear-gradient(220deg,
          #4169e1 0%,
          #6b85e8 20%,
          #95a5ef 40%,
          #bfc5f6 60%,
          #e7e9fd 80%,
          #ffffff 100%
        );
      background-size: 300% 100%;
      border-radius: 50%;
      position: relative;
      animation:
        heartbeat 1.2s infinite,
        gradient 2s ease-in-out infinite alternate;
    }

    @keyframes heartbeat {
      0% {
        transform: scale(1);
        opacity: 1;
      }

      25% {
        transform: scale(1.03);
      }

      50% {
        transform: scale(1.05);
      }

      75% {
        transform: scale(1.03);
      }

      100% {
        transform: scale(1);
      }
    }

    @keyframes gradient {
      0% {
        background-position: 100% 0%;
      }

      100% {
        background-position: 0% 0%;
      }
    }

    .big-date {
      min-width: 200px;
      max-width: 450px;
      min-height: 200px;
      max-height: 450px;

      .date-progress circle {
        stroke-width: 3;
      }
    }

    .add-quest {
      display: flex;
      align-items: center;

      ion-col {
        h2 {
          margin: 0;
        }

        display: flex;
        align-items: center;
      }
    }

    .quests {
      .no-quest-card {
        color: var(--text);
        margin: 16px 0;
        border: 1px solid var(--error);

        ion-card-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 20px;

          ion-col {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: left;

            ion-icon {
              font-size: 5rem;
              color: var(--error);
            }

            ion-button {
              margin-top: 16px;
              width: 80%;
            }
          }
        }
      }

      .quest-item {
        padding: 5px 0 5px 0;
        margin: 16px 0 16px 0;
        display: flex;
        flex-direction: column;

        ion-row {
          width: 100%;

          .quest-info {
            align-items: flex-start;

            h3 {
              font-size: 20px;
              color: var(--accent);
            }

            ion-text {
              font-size: 14px;
              margin-top: 5px;
              color: var(--text-secondary);
            }
          }

          ion-col {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;

            .quest-icon {
              font-size: 2rem;
            }

            .quest-streak {
              font-size: 1rem;
              color: var(--text-secondary);
            }
          }
        }

        .progress-container {
          margin-top: 10px;
          width: 100%;
          display: flex;
          justify-content: center;

          .progress-time,
          .progress {
            width: 100%;
            display: flex;
            flex-direction: column;
          }

          .progress-text {
            margin: 5px auto;
            color: var(--text-secondary);
            text-align: right;
          }
        }
      }
    }
  }
}

.add-quest-btn {
  --background: rgba(65, 105, 225, 0.1);
  --color: #4169E1;
  --border-radius: 6px;
  --padding-start: 12px;
  --padding-end: 12px;
  font-size: 14px;
  font-weight: 500;
  height: 32px;
  margin: 0;
}

.add-quest-btn:hover {
  --background: rgba(65, 105, 225, 0.2);
}


.quest-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.quest-item {
  background-color: #1C1C1E;
  border: 1px solid #2C2C2E;
  border-radius: 8px;
  padding: 12px;
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.quest-item:active {
  transform: scale(0.98);
}

.quest-item.completed {
  border-color: #4169E1;
}

.quest-item.completed .quest-title {
  color: #4169E1;
}

.quest-item.completed::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(65, 105, 225, 0.05);
  pointer-events: none;
}

.quest-icon {
  font-size: 20px;
  min-width: 24px;
  text-align: center;
}

.quest-info {
  flex-grow: 1;
}

.quest-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 2px;
  color: #4169E1;
}

.quest-description {
  color: #8E8E93;
  font-size: 12px;
  margin-bottom: 4px;
}


.progress,
.progress-time {
  color: var(--secondary-text);
  font-size: 12px;
}


/* Add a purple line between main quests and side quests */
.side-quests {
  position: relative;
  padding-top: 32px;
}


.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.quests h2,
.daily-side-quest h2 {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}


.progress-slider {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 4px;
  background: #2C2C2E;
  /* Use hardcoded color */
  outline: none;
  position: relative;
  /* Remove top and transform properties that cause vertical alignment issues */
}


.progress-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #4169E1;
  cursor: pointer;
  position: relative;
  margin-top: -4px;
  /* Only use margin-top for vertical centering */
}


.progress-slider::-moz-range-thumb {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #4169E1;
  cursor: pointer;
  border: none;
  position: relative;
  margin-top: 0;
  /* Firefox handles vertical centering differently */
}


.progress-slider::-webkit-slider-runnable-track {
  height: 4px;
  border-radius: 2px;
}


.progress-slider::-moz-range-track {
  height: 4px;
  border-radius: 2px;
}


/* Custom progress bar fill */
.progress-slider {
  /* Remove the static background gradient */
  background: var(--inactive-date);
}

/* Add dynamic styling for the progress slider */
input[type="range"].progress-slider {
  -webkit-appearance: none;
  appearance: none;
  height: 4px;
  border-radius: 2px;
  outline: none;
  position: relative;
  z-index: 1;
  /* Background will be set inline via JavaScript */
}

input[type="range"].progress-slider::-webkit-slider-runnable-track {
  height: 4px;
  border-radius: 2px;
  background: transparent;
}

input[type="range"].progress-slider::-moz-range-track {
  height: 4px;
  border-radius: 2px;
  background: transparent;
}

/* Style the filled part of the slider */
input[type="range"].progress-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #4169E1;
  /* Use hardcoded color */
  cursor: pointer;
  margin-top: -4px;
  /* Adjust to center the thumb */
  position: relative;
  z-index: 2;
}

input[type="range"].progress-slider::-moz-range-thumb {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #4169E1;
  /* Use hardcoded color */
  cursor: pointer;
  border: none;
  position: relative;
  margin-top: 0;
  /* Firefox handles vertical centering differently */
  z-index: 2;
}


.progress-text {
  font-size: 12px;
  color: var(--secondary-text);
  margin-top: 2px;
}



.container {
  width: 480px;
  margin: 0 auto;
  padding: 20px;
  overflow-y: auto;
  scrollbar-width: none;
  height: 100%;
  padding-bottom: 74px;
}

.container::-webkit-scrollbar {
  display: none;
  /* Chrome/Safari */
}


.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo img {
  height: 24px;
}

.logo span {
  font-size: 20px;
  font-weight: 600;
}

.page-title {
  font-size: 20px;
  font-weight: 600;
}


.week-calendar {
  margin-bottom: 32px;
}


.days,
.dates {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  text-align: center;
  gap: 8px;
}


.day-name {
  color: var(--secondary-text);
  font-size: 14px;
}

.date-content {
  position: relative;
  z-index: 1;
}

h2 {
  font-size: 20px;
  margin-bottom: 16px;
}


.side-quests::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 90%;
  height: 1px;
  background: linear-gradient(to right, transparent, #4B0082, transparent);
}


.calendar {
  margin: 20px 0;
  padding: 10px;
  background: var(--bg-secondary);
  border-radius: 8px;
}


.calendar-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
}


.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 5px;
  text-align: center;
}


.day-name {
  color: #8E8E93;
  font-size: 12px;
  font-weight: 500;
}


.day-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
  text-decoration: none;
  color: var(--text-primary);
  margin: 0 auto;
}


.day-number:hover {
  background: var(--bg-hover);
}


.day-number.selected {
  background: var(--primary-color);
  color: white;
}


.day-number.today {
  border: 2px solid var(--primary-color);
}


.nav-arrow {
  --background: transparent;
  --color: #FFFFFF;
  --border-radius: 50%;
  --padding-start: 0;
  --padding-end: 0;
  width: 32px;
  height: 32px;
  margin: 0;
  font-size: 18px;
  cursor: pointer;
}

.nav-arrow:hover {
  --background: rgba(255, 255, 255, 0.1);
}


.time-display {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color);
  margin-right: 16px;
}


/* Make number input spinners less prominent */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  opacity: 0.3;
}

/* Progress slider styling */
ion-range.progress-slider {
  --bar-height: 6px;
  --bar-border-radius: 3px;
  --knob-size: 16px;
  --bar-background: #2C2C2E;
  --bar-background-active: #4169E1;
  --knob-background: #4169E1;
  --pin-background: #4169E1;
  --pin-color: #FFFFFF;
  --step: 1;
  --tick-height: 0;
  --tick-width: 0;
  --tick-background: transparent;
  --tick-background-active: transparent;
  margin: 0;
  padding: 0;
}

/* For standard HTML sliders (fallback) */
.progress-slider {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 6px;
  border-radius: 3px;
  outline: none;
  background: linear-gradient(to right, #4169E1 0%, #4169E1 var(--progress-value), #2C2C2E var(--progress-value), #2C2C2E 100%);
}

/* Hide tick marks completely */
ion-range.progress-slider::part(tick) {
  display: none !important;
}

ion-range.progress-slider::part(tick-active) {
  display: none !important;
}

.progress-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #4169E1;
  cursor: pointer;
  margin-top: -5px;
}

.progress-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #4169E1;
  cursor: pointer;
  margin-top: 0;
  border: none;
}

.daily-side-quest h2 {
  padding-bottom: 20px;
}

/* Modern Bottom Sheet Modal Styling */
.add-quest-modal {
  --background: transparent;
  --backdrop-opacity: 0.6;
  --width: 100vw;
  --height: 90vh;
  --border-radius: 24px 24px 0 0;
  --box-shadow: 0 -10px 40px rgba(0, 0, 0, 0.3);

  /* Position modal at bottom */
  ion-backdrop {
    background: rgba(0, 0, 0, 0.6);
  }

  .modal-wrapper {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 90vh;
    border-radius: 24px 24px 0 0;
    overflow: hidden;
    background: linear-gradient(135deg,
      var(--surface) 0%,
      var(--surface-alt) 50%,
      var(--bg) 100%);
    border: 1px solid var(--border);
    border-bottom: none;
    box-shadow: 0 -10px 40px rgba(0, 0, 0, 0.3);
  }

  /* Modal Header */
  .modal-header {
    --background: transparent;
    --border-color: var(--border);
    border-bottom: 1px solid var(--border);

    ion-toolbar {
      --background: transparent;
      --padding-start: 24px;
      --padding-end: 24px;
      --min-height: 64px;

      .modal-title {
        .title-content {
          display: flex;
          align-items: center;
          gap: 12px;

          .title-icon {
            font-size: 24px;
            filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.3));
          }

          span {
            font-size: 20px;
            font-weight: 700;
            color: var(--text);
            background: linear-gradient(135deg, var(--text), var(--text-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
          }
        }
      }

      .close-button {
        --color: var(--text-secondary);
        --background: var(--surface-alt);
        --border-radius: 12px;
        --padding-start: 12px;
        --padding-end: 12px;
        height: 40px;
        width: 40px;
        transition: all 0.3s ease;

        &:hover {
          --background: var(--border);
          --color: var(--text);
          transform: scale(1.05);
        }
      }
    }
  }

  /* Modal Content */
  .modal-content {
    --background: transparent;
    --padding-start: 0;
    --padding-end: 0;
    --padding-top: 0;
    --padding-bottom: 0;

    .modal-body {
      padding: 24px;
      max-height: calc(90vh - 140px);
      overflow-y: auto;
      scrollbar-width: thin;
      scrollbar-color: var(--text-muted) transparent;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: var(--text-muted);
        border-radius: 3px;
      }
    }
  }

  /* Form Sections */
  .form-section {
    margin-bottom: 32px;

    .section-header {
      margin-bottom: 20px;

      h3 {
        font-size: 18px;
        font-weight: 600;
        color: var(--text);
        margin: 0 0 8px 0;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .section-divider {
        height: 2px;
        background: linear-gradient(90deg,
          var(--accent) 0%,
          rgba(65, 105, 225, 0.3) 50%,
          transparent 100%);
        border-radius: 1px;
      }
    }
  }

  /* Quest Identity Row */
  .quest-identity-row {
    display: flex;
    gap: 16px;
    margin-bottom: 20px;

    .emoji-container {
      position: relative;
      flex-shrink: 0;
      width: 80px;

      .emoji-input {
        @extend .dark-input;
        height: 56px;
        text-align: center;
        font-size: 24px;
        --border-radius: 16px;

        &:focus-within {
          --border-color: var(--accent);
          box-shadow: 0 0 0 4px var(--accent-glow);
        }
      }

      .emoji-label {
        position: absolute;
        bottom: -20px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 12px;
        color: var(--text-secondary);
        font-weight: 500;
      }
    }

    .name-container {
      flex: 1;
      position: relative;
    }
  }

  /* Input Groups with Floating Labels */
  .input-group {
    position: relative;
    margin-bottom: 24px;

    .floating-label {
      position: absolute;
      left: 16px;
      top: 50%;
      transform: translateY(-50%);
      color: var(--text-secondary);
      font-size: 16px;
      font-weight: 500;
      pointer-events: none;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      z-index: 1;
      background: transparent;
      padding: 0 4px;
    }

    /* Floating label animation for inputs */
    ion-input:focus-within + .floating-label,
    ion-input:not([value=""]) + .floating-label,
    ion-input.has-value + .floating-label,
    ion-textarea:focus-within + .floating-label,
    ion-textarea:not([value=""]) + .floating-label,
    ion-select:focus-within + .floating-label,
    ion-select.has-value + .floating-label {
      top: 0;
      transform: translateY(-50%);
      font-size: 12px;
      color: var(--accent);
      background: var(--surface);
    }
  }

  /* Use existing dark-input class with modifications */
  .name-input,
  .description-input,
  .goal-input {
    @extend .dark-input;
    --border-radius: 16px;
    --padding-top: 20px;
    --padding-bottom: 12px;
    height: 56px;
    transition: all 0.3s ease;

    &:focus-within {
      --border-color: var(--accent);
      box-shadow: 0 0 0 4px var(--accent-glow);
    }
  }

  .description-input {
    height: auto;
    min-height: 80px;
    --padding-top: 24px;
    resize: vertical;
  }

  /* Modern Select Styling using dark-input base */
  .modern-select {
    @extend .dark-input;
    --border-radius: 16px;
    --padding-top: 20px;
    --padding-bottom: 12px;
    height: 56px;
    transition: all 0.3s ease;

    &:focus-within {
      --border-color: var(--accent);
      box-shadow: 0 0 0 4px var(--accent-glow);
    }

    &.unit-select {
      min-width: 120px;
    }
  }

  /* Goal Row */
  .goal-row {
    display: flex;
    gap: 16px;
    margin-bottom: 20px;

    .goal-value-container {
      flex: 1;
      position: relative;
    }

    .goal-unit-container {
      flex: 1;
      position: relative;
    }
  }

  /* Priority Warning */
  .priority-warning {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
    padding: 12px 16px;
    background: rgba(250, 204, 21, 0.1);
    border: 1px solid var(--warning);
    border-radius: 12px;
    font-size: 14px;
    color: var(--warning);

    ion-icon {
      font-size: 16px;
    }
  }

  /* Schedule Sections */
  .schedule-section {
    margin-top: 20px;
    padding: 20px;
    background: var(--surface-alt);
    border: 1px solid var(--border);
    border-radius: 16px;

    h4 {
      font-size: 16px;
      font-weight: 600;
      color: var(--text);
      margin: 0 0 16px 0;
    }

    .days-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
      gap: 12px;
    }

    .month-days-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
      gap: 8px;
    }

    .day-item,
    .month-day-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      padding: 12px 8px;
      background: var(--surface);
      border: 1px solid var(--border);
      border-radius: 12px;
      transition: all 0.3s ease;
      cursor: pointer;

      &:hover {
        background: var(--surface-alt);
        border-color: var(--accent);
      }

      .modern-checkbox {
        --background: transparent;
        --background-checked: var(--accent);
        --border-color: var(--border);
        --border-color-checked: var(--accent);
        --checkmark-color: var(--text);
        --size: 20px;
        --border-radius: 6px;
        margin: 0;
      }

      .day-label,
      .month-day-label {
        font-size: 14px;
        font-weight: 500;
        color: var(--text);
        text-align: center;
        margin: 0;
      }
    }
  }

  /* Modal Footer */
  .modal-footer {
    --background: transparent;
    --border-color: var(--border);
    border-top: 1px solid var(--border);

    ion-toolbar {
      --background: transparent;
      --padding-start: 24px;
      --padding-end: 24px;
      --min-height: 80px;

      .footer-buttons {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        gap: 16px;

        .cancel-btn {
          --color: var(--text-secondary);
          --background: transparent;
          --border-radius: 12px;
          --padding-start: 20px;
          --padding-end: 20px;
          height: 48px;
          font-weight: 600;
          transition: all 0.3s ease;

          &:hover {
            --color: var(--text);
            --background: var(--surface-alt);
          }
        }

        .create-quest-btn {
          @extend .blue-button;
          --border-radius: 16px;
          --padding-start: 24px;
          --padding-end: 24px;
          height: 48px;
          font-weight: 700;
          font-size: 16px;
          box-shadow: 0 8px 24px var(--accent-glow);
          transition: all 0.3s ease;
          flex: 1;
          max-width: 200px;

          &:hover:not([disabled]) {
            transform: translateY(-2px);
            box-shadow: 0 12px 32px var(--accent-glow);
          }

          &:active:not([disabled]) {
            transform: translateY(0);
          }

          &[disabled] {
            --background: var(--surface);
            --color: var(--text-muted);
            box-shadow: none;
            opacity: 0.6;
          }

          ion-icon {
            font-size: 18px;
          }
        }
      }
    }
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .modal-header ion-toolbar {
      --padding-start: 16px;
      --padding-end: 16px;
      --min-height: 56px;

      .modal-title .title-content {
        gap: 8px;

        .title-icon {
          font-size: 20px;
        }

        span {
          font-size: 18px;
        }
      }
    }

    .modal-body {
      padding: 16px;
    }

    .quest-identity-row {
      flex-direction: column;
      gap: 12px;

      .emoji-container {
        width: 100%;
        display: flex;
        justify-content: center;

        .emoji-input {
          width: 80px;
        }
      }
    }

    .goal-row {
      flex-direction: column;
      gap: 12px;
    }

    .schedule-section {
      .days-grid {
        grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
        gap: 8px;
      }

      .month-days-grid {
        grid-template-columns: repeat(auto-fill, minmax(50px, 1fr));
        gap: 6px;
      }
    }

    .modal-footer ion-toolbar {
      --padding-start: 16px;
      --padding-end: 16px;
      --min-height: 72px;

      .footer-buttons {
        flex-direction: column;
        gap: 12px;

        .cancel-btn,
        .create-quest-btn {
          width: 100%;
          max-width: none;
        }
      }
    }
  }
}